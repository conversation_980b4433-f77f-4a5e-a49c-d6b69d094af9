/* ===== CSS VARIABLES ===== */
:root {
    /* Light Theme Colors */
    --primary-color: #2563eb;
    --secondary-color: #1e40af;
    --accent-color: #3b82f6;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --danger-color: #ef4444;
    
    --bg-primary: #ffffff;
    --bg-secondary: #f8fafc;
    --bg-tertiary: #f1f5f9;
    --bg-glass: rgba(255, 255, 255, 0.25);
    
    --text-primary: #1e293b;
    --text-secondary: #64748b;
    --text-muted: #94a3b8;
    
    --border-color: #e2e8f0;
    --shadow-light: 0 1px 3px rgba(0, 0, 0, 0.1);
    --shadow-medium: 0 4px 6px rgba(0, 0, 0, 0.1);
    --shadow-heavy: 0 10px 25px rgba(0, 0, 0, 0.15);
    
    /* Typography */
    --font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Robot<PERSON>, sans-serif;
    --font-size-xs: 0.75rem;
    --font-size-sm: 0.875rem;
    --font-size-base: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    --font-size-2xl: 1.5rem;
    --font-size-3xl: 1.875rem;
    --font-size-4xl: 2.25rem;
    
    /* Spacing */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-2xl: 3rem;
    
    /* Border Radius */
    --radius-sm: 0.375rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;
    
    /* Transitions */
    --transition-fast: 0.15s ease-in-out;
    --transition-normal: 0.3s ease-in-out;
    --transition-slow: 0.5s ease-in-out;
}

/* Dark Theme */
[data-theme="dark"] {
    --bg-primary: #0f172a;
    --bg-secondary: #1e293b;
    --bg-tertiary: #334155;
    --bg-glass: rgba(15, 23, 42, 0.25);
    
    --text-primary: #f8fafc;
    --text-secondary: #cbd5e1;
    --text-muted: #64748b;
    
    --border-color: #334155;
}

/* ===== GLOBAL STYLES ===== */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
    font-size: 16px;
}

body {
    font-family: var(--font-family);
    font-size: var(--font-size-base);
    line-height: 1.6;
    color: var(--text-primary);
    background-color: var(--bg-primary);
    transition: background-color var(--transition-normal), color var(--transition-normal);
    overflow-x: hidden;
}

/* ===== LOADING SCREEN ===== */
.loading-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
    transition: opacity var(--transition-slow), visibility var(--transition-slow);
}

.loading-screen.hidden {
    opacity: 0;
    visibility: hidden;
}

.loading-spinner {
    text-align: center;
    color: white;
}

.spinner {
    width: 50px;
    height: 50px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-top: 3px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 1rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* ===== NAVIGATION ===== */
.navbar-custom {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid var(--border-color);
    padding: var(--spacing-md) 0;
    transition: all var(--transition-normal);
}

[data-theme="dark"] .navbar-custom {
    background: rgba(15, 23, 42, 0.95);
}

.navbar-custom.scrolled {
    padding: var(--spacing-sm) 0;
    box-shadow: var(--shadow-medium);
}

.navbar-brand {
    font-weight: 700;
    font-size: var(--font-size-xl);
    color: var(--primary-color) !important;
    text-decoration: none;
}

.brand-text {
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.navbar-nav .nav-link {
    color: var(--text-primary) !important;
    font-weight: 500;
    margin: 0 var(--spacing-sm);
    padding: var(--spacing-sm) var(--spacing-md) !important;
    border-radius: var(--radius-md);
    transition: all var(--transition-fast);
    position: relative;
}

.navbar-nav .nav-link:hover {
    color: var(--primary-color) !important;
    background-color: var(--bg-secondary);
}

.navbar-nav .nav-link.active {
    color: var(--primary-color) !important;
    background-color: var(--bg-secondary);
}

.btn-download {
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color)) !important;
    color: white !important;
    border: none;
    padding: var(--spacing-sm) var(--spacing-lg) !important;
    border-radius: var(--radius-md);
    font-weight: 600;
    transition: all var(--transition-fast);
}

.btn-download:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
    color: white !important;
}

.theme-toggle {
    background: none;
    border: 2px solid var(--border-color);
    color: var(--text-primary);
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: var(--spacing-md);
    transition: all var(--transition-fast);
    cursor: pointer;
}

.theme-toggle:hover {
    background-color: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.navbar-toggler {
    border: none;
    padding: var(--spacing-xs);
}

.navbar-toggler:focus {
    box-shadow: none;
}

.navbar-toggler-icon {
    color: var(--text-primary);
    font-size: var(--font-size-lg);
}

/* ===== MAIN CONTENT ===== */
.main-content {
    margin-top: 80px;
    min-height: calc(100vh - 80px);
}

/* ===== HERO SECTION ===== */
.hero-section {
    min-height: 100vh;
    display: flex;
    align-items: center;
    background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 100%);
    position: relative;
    overflow: hidden;
}

.hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><defs><radialGradient id="a" cx="50%" cy="50%"><stop offset="0%" stop-color="%23ffffff" stop-opacity="0.1"/><stop offset="100%" stop-color="%23ffffff" stop-opacity="0"/></radialGradient></defs><circle cx="200" cy="200" r="100" fill="url(%23a)"/><circle cx="800" cy="300" r="150" fill="url(%23a)"/><circle cx="400" cy="700" r="120" fill="url(%23a)"/></svg>');
    opacity: 0.5;
    animation: float 20s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-20px) rotate(180deg); }
}

.hero-content {
    position: relative;
    z-index: 2;
}

.hero-title {
    font-size: clamp(2.5rem, 5vw, 4rem);
    font-weight: 700;
    margin-bottom: var(--spacing-lg);
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: slideInUp 1s ease-out;
}

.hero-subtitle {
    font-size: var(--font-size-xl);
    color: var(--text-secondary);
    margin-bottom: var(--spacing-xl);
    animation: slideInUp 1s ease-out 0.2s both;
}

.hero-description {
    font-size: var(--font-size-lg);
    color: var(--text-muted);
    margin-bottom: var(--spacing-2xl);
    max-width: 600px;
    animation: slideInUp 1s ease-out 0.4s both;
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.hero-buttons {
    display: flex;
    gap: var(--spacing-lg);
    flex-wrap: wrap;
    animation: slideInUp 1s ease-out 0.6s both;
}

.btn-primary-custom {
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    border: none;
    color: white;
    padding: var(--spacing-md) var(--spacing-xl);
    border-radius: var(--radius-lg);
    font-weight: 600;
    font-size: var(--font-size-lg);
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-sm);
    transition: all var(--transition-fast);
    box-shadow: var(--shadow-medium);
}

.btn-primary-custom:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-heavy);
    color: white;
}

.btn-secondary-custom {
    background: transparent;
    border: 2px solid var(--primary-color);
    color: var(--primary-color);
    padding: var(--spacing-md) var(--spacing-xl);
    border-radius: var(--radius-lg);
    font-weight: 600;
    font-size: var(--font-size-lg);
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-sm);
    transition: all var(--transition-fast);
}

.btn-secondary-custom:hover {
    background: var(--primary-color);
    color: white;
    transform: translateY(-3px);
}

.hero-image {
    position: relative;
    animation: slideInRight 1s ease-out 0.8s both;
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.profile-image {
    width: 100%;
    max-width: 400px;
    height: auto;
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-heavy);
    transition: transform var(--transition-normal);
}

.profile-image:hover {
    transform: scale(1.05);
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 768px) {
    .hero-section {
        text-align: center;
        padding: var(--spacing-2xl) 0;
    }
    
    .hero-buttons {
        justify-content: center;
    }
    
    .btn-primary-custom,
    .btn-secondary-custom {
        width: 100%;
        justify-content: center;
    }
    
    .navbar-nav .nav-link {
        margin: var(--spacing-xs) 0;
    }
    
    .theme-toggle {
        margin: var(--spacing-md) auto 0;
    }
}

/* ===== UTILITY CLASSES ===== */
.text-gradient {
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.glass-effect {
    background: var(--bg-glass);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.fade-in {
    opacity: 0;
    animation: fadeIn 1s ease-out forwards;
}

@keyframes fadeIn {
    to {
        opacity: 1;
    }
}

.slide-up {
    transform: translateY(30px);
    opacity: 0;
    animation: slideUp 0.8s ease-out forwards;
}

@keyframes slideUp {
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

/* ===== SECTIONS ===== */
.section {
    padding: var(--spacing-2xl) 0;
}

.section-title {
    font-size: var(--font-size-3xl);
    font-weight: 700;
    text-align: center;
    margin-bottom: var(--spacing-xl);
    color: var(--text-primary);
}

.section-subtitle {
    font-size: var(--font-size-lg);
    color: var(--text-secondary);
    text-align: center;
    margin-bottom: var(--spacing-2xl);
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

/* ===== SKILLS SECTION ===== */
.skills-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: var(--spacing-xl);
    margin-top: var(--spacing-2xl);
}

.skill-category {
    background: var(--bg-secondary);
    border-radius: var(--radius-lg);
    padding: var(--spacing-xl);
    box-shadow: var(--shadow-light);
    transition: all var(--transition-normal);
    border: 1px solid var(--border-color);
}

.skill-category:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-heavy);
}

.skill-category h4 {
    color: var(--primary-color);
    margin-bottom: var(--spacing-lg);
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.skill-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--spacing-md);
    padding: var(--spacing-sm);
    background: var(--bg-primary);
    border-radius: var(--radius-md);
    transition: all var(--transition-fast);
}

.skill-item:hover {
    background: var(--bg-tertiary);
}

.skill-info {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.skill-icon {
    font-size: var(--font-size-lg);
    color: var(--primary-color);
}

.skill-name {
    font-weight: 500;
    color: var(--text-primary);
}

.skill-level {
    display: flex;
    gap: 2px;
}

.skill-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: var(--border-color);
    transition: background-color var(--transition-fast);
}

.skill-dot.active {
    background: var(--primary-color);
}

/* ===== EXPERIENCE SECTION ===== */
.experience-timeline {
    position: relative;
    margin-top: var(--spacing-2xl);
}

.experience-timeline::before {
    content: '';
    position: absolute;
    left: 50%;
    top: 0;
    bottom: 0;
    width: 2px;
    background: var(--border-color);
    transform: translateX(-50%);
}

.experience-item {
    position: relative;
    margin-bottom: var(--spacing-2xl);
    display: flex;
    align-items: center;
}

.experience-item:nth-child(odd) {
    flex-direction: row;
}

.experience-item:nth-child(even) {
    flex-direction: row-reverse;
}

.experience-content {
    width: calc(50% - 30px);
    background: var(--bg-secondary);
    padding: var(--spacing-xl);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-light);
    border: 1px solid var(--border-color);
    transition: all var(--transition-normal);
}

.experience-content:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-heavy);
}

.experience-date {
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    background: var(--primary-color);
    color: white;
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--radius-md);
    font-size: var(--font-size-sm);
    font-weight: 600;
    white-space: nowrap;
}

.experience-title {
    font-size: var(--font-size-xl);
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-sm);
}

.experience-company {
    color: var(--primary-color);
    font-weight: 500;
    margin-bottom: var(--spacing-xs);
}

.experience-location {
    color: var(--text-muted);
    font-size: var(--font-size-sm);
    margin-bottom: var(--spacing-md);
}

.experience-description {
    color: var(--text-secondary);
    margin-bottom: var(--spacing-md);
    line-height: 1.6;
}

.experience-achievements {
    list-style: none;
    margin-bottom: var(--spacing-md);
}

.experience-achievements li {
    position: relative;
    padding-left: var(--spacing-lg);
    margin-bottom: var(--spacing-sm);
    color: var(--text-secondary);
}

.experience-achievements li::before {
    content: '✓';
    position: absolute;
    left: 0;
    color: var(--success-color);
    font-weight: bold;
}

.experience-technologies {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-sm);
}

.tech-tag {
    background: var(--primary-color);
    color: white;
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-sm);
    font-size: var(--font-size-xs);
    font-weight: 500;
}

/* ===== PROJECTS SECTION ===== */
.project-filters {
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-2xl);
}

.filter-btn {
    background: transparent;
    border: 2px solid var(--border-color);
    color: var(--text-secondary);
    padding: var(--spacing-sm) var(--spacing-lg);
    border-radius: var(--radius-md);
    font-weight: 500;
    cursor: pointer;
    transition: all var(--transition-fast);
}

.filter-btn:hover,
.filter-btn.active {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.projects-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: var(--spacing-xl);
}

.project-card {
    background: var(--bg-secondary);
    border-radius: var(--radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow-light);
    border: 1px solid var(--border-color);
    transition: all var(--transition-normal);
    position: relative;
}

.project-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-heavy);
}

.project-image {
    width: 100%;
    height: 200px;
    object-fit: cover;
    transition: transform var(--transition-normal);
}

.project-card:hover .project-image {
    transform: scale(1.1);
}

.project-content {
    padding: var(--spacing-xl);
}

.project-title {
    font-size: var(--font-size-xl);
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-sm);
}

.project-description {
    color: var(--text-secondary);
    margin-bottom: var(--spacing-lg);
    line-height: 1.6;
}

.project-technologies {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-lg);
}

.project-links {
    display: flex;
    gap: var(--spacing-md);
}

.project-link {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-xs);
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 500;
    transition: all var(--transition-fast);
}

.project-link:hover {
    color: var(--secondary-color);
    transform: translateX(3px);
}

.featured-badge {
    position: absolute;
    top: var(--spacing-md);
    right: var(--spacing-md);
    background: var(--warning-color);
    color: white;
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-sm);
    font-size: var(--font-size-xs);
    font-weight: 600;
}

/* ===== RESPONSIVE DESIGN FOR SECTIONS ===== */
@media (max-width: 768px) {
    .experience-timeline::before {
        left: 20px;
    }

    .experience-item {
        flex-direction: column !important;
        align-items: flex-start;
        padding-left: 40px;
    }

    .experience-content {
        width: 100%;
    }

    .experience-date {
        position: static;
        transform: none;
        margin-bottom: var(--spacing-md);
        align-self: flex-start;
    }

    .projects-grid {
        grid-template-columns: 1fr;
    }

    .skills-grid {
        grid-template-columns: 1fr;
    }
}

/* ===== CONTACT SECTION ===== */
.contact-form {
    max-width: 600px;
    margin: 0 auto;
    background: var(--bg-secondary);
    padding: var(--spacing-2xl);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-light);
    border: 1px solid var(--border-color);
}

.form-group {
    margin-bottom: var(--spacing-lg);
}

.form-label {
    display: block;
    margin-bottom: var(--spacing-sm);
    font-weight: 500;
    color: var(--text-primary);
}

.form-control {
    width: 100%;
    padding: var(--spacing-md);
    border: 2px solid var(--border-color);
    border-radius: var(--radius-md);
    background: var(--bg-primary);
    color: var(--text-primary);
    font-size: var(--font-size-base);
    transition: all var(--transition-fast);
}

.form-control:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.form-control.is-invalid {
    border-color: var(--danger-color);
}

.invalid-feedback {
    color: var(--danger-color);
    font-size: var(--font-size-sm);
    margin-top: var(--spacing-xs);
}

.btn-submit {
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    border: none;
    color: white;
    padding: var(--spacing-md) var(--spacing-2xl);
    border-radius: var(--radius-md);
    font-weight: 600;
    font-size: var(--font-size-lg);
    cursor: pointer;
    transition: all var(--transition-fast);
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
}

.btn-submit:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
}

.btn-submit:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

.contact-info {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-xl);
    margin-bottom: var(--spacing-2xl);
}

.contact-item {
    text-align: center;
    padding: var(--spacing-xl);
    background: var(--bg-secondary);
    border-radius: var(--radius-lg);
    border: 1px solid var(--border-color);
    transition: all var(--transition-normal);
}

.contact-item:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-medium);
}

.contact-icon {
    font-size: var(--font-size-3xl);
    color: var(--primary-color);
    margin-bottom: var(--spacing-md);
}

.contact-title {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-sm);
}

.contact-details {
    color: var(--text-secondary);
}

.contact-link {
    color: var(--primary-color);
    text-decoration: none;
    transition: color var(--transition-fast);
}

.contact-link:hover {
    color: var(--secondary-color);
}

/* ===== FOOTER ===== */
.footer-custom {
    background: var(--bg-secondary);
    border-top: 1px solid var(--border-color);
    padding: var(--spacing-2xl) 0 var(--spacing-lg);
    margin-top: var(--spacing-2xl);
}

.footer-info h5 {
    color: var(--text-primary);
    margin-bottom: var(--spacing-md);
    font-weight: 600;
}

.footer-info p {
    color: var(--text-secondary);
    margin-bottom: var(--spacing-lg);
}

.social-links {
    display: flex;
    gap: var(--spacing-md);
}

.social-links a {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background: var(--primary-color);
    color: white;
    border-radius: 50%;
    text-decoration: none;
    transition: all var(--transition-fast);
}

.social-links a:hover {
    background: var(--secondary-color);
    transform: translateY(-3px);
}

.footer-links h6,
.footer-contact h6 {
    color: var(--text-primary);
    margin-bottom: var(--spacing-md);
    font-weight: 600;
}

.footer-links ul {
    list-style: none;
    padding: 0;
}

.footer-links li {
    margin-bottom: var(--spacing-sm);
}

.footer-links a {
    color: var(--text-secondary);
    text-decoration: none;
    transition: color var(--transition-fast);
}

.footer-links a:hover {
    color: var(--primary-color);
}

.footer-contact p {
    color: var(--text-secondary);
    margin-bottom: var(--spacing-sm);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.footer-contact i {
    color: var(--primary-color);
    width: 16px;
}

.footer-divider {
    border: none;
    height: 1px;
    background: var(--border-color);
    margin: var(--spacing-xl) 0;
}

.footer-copyright {
    color: var(--text-muted);
    margin: 0;
    font-size: var(--font-size-sm);
}

/* ===== BACK TO TOP BUTTON ===== */
.back-to-top {
    position: fixed;
    bottom: 30px;
    right: 30px;
    width: 50px;
    height: 50px;
    background: var(--primary-color);
    color: white;
    border: none;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-lg);
    box-shadow: var(--shadow-medium);
    transition: all var(--transition-fast);
    opacity: 0;
    visibility: hidden;
    z-index: 1000;
}

.back-to-top.visible {
    opacity: 1;
    visibility: visible;
}

.back-to-top:hover {
    background: var(--secondary-color);
    transform: translateY(-3px);
}

/* ===== ALERTS ===== */
.alert {
    padding: var(--spacing-md);
    border-radius: var(--radius-md);
    margin-bottom: var(--spacing-lg);
    border: 1px solid transparent;
}

.alert-success {
    background: rgba(16, 185, 129, 0.1);
    border-color: var(--success-color);
    color: var(--success-color);
}

.alert-danger {
    background: rgba(239, 68, 68, 0.1);
    border-color: var(--danger-color);
    color: var(--danger-color);
}

/* ===== ANIMATIONS ===== */
@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

.loading {
    animation: pulse 1.5s ease-in-out infinite;
}

/* ===== SCROLL ANIMATIONS ===== */
.fade-in-up {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.8s ease-out;
}

.fade-in-up.visible {
    opacity: 1;
    transform: translateY(0);
}

.fade-in-left {
    opacity: 0;
    transform: translateX(-30px);
    transition: all 0.8s ease-out;
}

.fade-in-left.visible {
    opacity: 1;
    transform: translateX(0);
}

.fade-in-right {
    opacity: 0;
    transform: translateX(30px);
    transition: all 0.8s ease-out;
}

.fade-in-right.visible {
    opacity: 1;
    transform: translateX(0);
}

/* ===== HOMEPAGE SPECIFIC STYLES ===== */
.stat-card {
    background: var(--bg-secondary);
    padding: var(--spacing-xl);
    border-radius: var(--radius-lg);
    border: 1px solid var(--border-color);
    transition: all var(--transition-normal);
    height: 100%;
}

.stat-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-heavy);
}

.stat-icon {
    font-size: var(--font-size-3xl);
    color: var(--primary-color);
    margin-bottom: var(--spacing-md);
}

.stat-number {
    font-size: var(--font-size-3xl);
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: var(--spacing-sm);
}

.stat-label {
    color: var(--text-secondary);
    font-weight: 500;
    margin: 0;
}

.tech-tags {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-sm);
    margin-top: var(--spacing-md);
}

.project-card-preview {
    background: var(--bg-secondary);
    border-radius: var(--radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow-light);
    border: 1px solid var(--border-color);
    transition: all var(--transition-normal);
    height: 100%;
}

.project-card-preview:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-heavy);
}

.project-image-preview {
    position: relative;
    overflow: hidden;
    height: 200px;
}

.project-image-preview img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform var(--transition-normal);
}

.project-card-preview:hover .project-image-preview img {
    transform: scale(1.1);
}

.project-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(37, 99, 235, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity var(--transition-normal);
}

.project-card-preview:hover .project-overlay {
    opacity: 1;
}

.project-link-preview {
    color: white;
    font-size: var(--font-size-xl);
    text-decoration: none;
    padding: var(--spacing-md);
    border: 2px solid white;
    border-radius: 50%;
    transition: all var(--transition-fast);
}

.project-link-preview:hover {
    background: white;
    color: var(--primary-color);
}

.project-content-preview {
    padding: var(--spacing-xl);
}

.project-content-preview h4 {
    color: var(--text-primary);
    margin-bottom: var(--spacing-sm);
    font-weight: 600;
}

.project-content-preview p {
    color: var(--text-secondary);
    margin-bottom: var(--spacing-md);
    line-height: 1.6;
}

.project-tech-preview {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-sm);
}

.bg-gradient {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
}

.cta-buttons {
    display: flex;
    gap: var(--spacing-lg);
    justify-content: center;
    flex-wrap: wrap;
}

.about-image {
    text-align: center;
}

.skills-preview {
    margin-top: var(--spacing-xl);
    padding: var(--spacing-lg);
    background: var(--bg-secondary);
    border-radius: var(--radius-md);
    border: 1px solid var(--border-color);
}

.skills-preview h5 {
    color: var(--text-primary);
    margin-bottom: var(--spacing-md);
    font-weight: 600;
}

/* ===== ABOUT PAGE STYLES ===== */
.personal-info {
    margin-top: var(--spacing-xl);
    padding: var(--spacing-lg);
    background: var(--bg-secondary);
    border-radius: var(--radius-md);
    border: 1px solid var(--border-color);
}

.info-list {
    list-style: none;
    padding: 0;
}

.info-list li {
    margin-bottom: var(--spacing-sm);
    color: var(--text-secondary);
}

.info-list strong {
    color: var(--text-primary);
}

.about-image-container {
    position: relative;
    text-align: center;
}

.about-photo {
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-heavy);
    position: relative;
    z-index: 2;
    max-width: 400px;
}

.about-decoration {
    position: absolute;
    top: 20px;
    right: 20px;
    width: 100%;
    height: 100%;
    border: 3px solid var(--primary-color);
    border-radius: var(--radius-xl);
    z-index: 1;
}

.education-card {
    background: var(--bg-secondary);
    padding: var(--spacing-xl);
    border-radius: var(--radius-lg);
    border: 1px solid var(--border-color);
    text-align: center;
    transition: all var(--transition-normal);
    height: 100%;
}

.education-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-heavy);
}

.education-icon {
    font-size: var(--font-size-3xl);
    color: var(--primary-color);
    margin-bottom: var(--spacing-md);
}

.education-degree {
    color: var(--text-primary);
    font-weight: 600;
    margin-bottom: var(--spacing-sm);
    font-size: var(--font-size-lg);
}

.education-institution {
    color: var(--primary-color);
    font-weight: 500;
    margin-bottom: var(--spacing-sm);
    font-size: var(--font-size-base);
}

.education-location {
    color: var(--text-muted);
    font-size: var(--font-size-sm);
    margin-bottom: var(--spacing-sm);
}

.education-date {
    color: var(--text-secondary);
    font-weight: 500;
    margin-bottom: var(--spacing-md);
}

.education-description {
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
    line-height: 1.6;
}

.volunteer-card {
    background: var(--bg-secondary);
    padding: var(--spacing-2xl);
    border-radius: var(--radius-lg);
    border: 1px solid var(--border-color);
    text-align: center;
    box-shadow: var(--shadow-light);
}

.volunteer-icon {
    font-size: var(--font-size-4xl);
    color: var(--danger-color);
    margin-bottom: var(--spacing-lg);
}

.volunteer-card h4 {
    color: var(--text-primary);
    font-weight: 600;
    margin-bottom: var(--spacing-sm);
}

.volunteer-card h5 {
    color: var(--primary-color);
    font-weight: 500;
    margin-bottom: var(--spacing-sm);
}

.volunteer-date {
    color: var(--text-muted);
    font-weight: 500;
    margin-bottom: var(--spacing-lg);
}

.volunteer-description {
    color: var(--text-secondary);
    line-height: 1.6;
    margin-bottom: var(--spacing-xl);
}

.volunteer-impact {
    display: flex;
    justify-content: center;
    gap: var(--spacing-xl);
    flex-wrap: wrap;
}

.impact-item {
    text-align: center;
}

.impact-item strong {
    display: block;
    font-size: var(--font-size-xl);
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: var(--spacing-xs);
}

.impact-item span {
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
}

/* ===== RESPONSIVE DESIGN FOR ABOUT PAGE ===== */
@media (max-width: 768px) {
    .about-decoration {
        display: none;
    }

    .volunteer-impact {
        gap: var(--spacing-lg);
    }

    .personal-info .row {
        text-align: center;
    }
}

/* ===== PROJECTS PAGE STYLES ===== */
.project-image-container {
    position: relative;
    overflow: hidden;
    border-radius: var(--radius-lg) var(--radius-lg) 0 0;
}

.project-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(37, 99, 235, 0.95);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity var(--transition-normal);
}

.project-card:hover .project-overlay {
    opacity: 1;
}

.project-actions {
    display: flex;
    gap: var(--spacing-md);
}

.project-action-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 50px;
    height: 50px;
    background: white;
    color: var(--primary-color);
    border: none;
    border-radius: 50%;
    text-decoration: none;
    font-size: var(--font-size-lg);
    transition: all var(--transition-fast);
    cursor: pointer;
}

.project-action-btn:hover {
    background: var(--primary-color);
    color: white;
    transform: scale(1.1);
}

.project-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-md);
}

.project-category {
    background: var(--primary-color);
    color: white;
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-sm);
    font-size: var(--font-size-xs);
    font-weight: 600;
}

.project-date {
    color: var(--text-muted);
    font-size: var(--font-size-sm);
    font-weight: 500;
}

.github-card {
    background: var(--bg-secondary);
    padding: var(--spacing-xl);
    border-radius: var(--radius-lg);
    border: 1px solid var(--border-color);
    height: 100%;
    transition: all var(--transition-normal);
}

.github-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-heavy);
}

.github-header {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
}

.github-header i {
    font-size: var(--font-size-2xl);
    color: var(--primary-color);
}

.github-header h4 {
    color: var(--text-primary);
    margin: 0;
    font-weight: 600;
}

.github-content p {
    color: var(--text-secondary);
    margin-bottom: var(--spacing-lg);
    line-height: 1.6;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-lg);
}

.stat-item {
    text-align: center;
}

.stat-item strong {
    display: block;
    font-size: var(--font-size-lg);
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: var(--spacing-xs);
}

.stat-item span {
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
}

.empty-state {
    padding: var(--spacing-2xl);
}

.empty-state i {
    color: var(--text-muted);
}

.empty-state h4 {
    color: var(--text-primary);
    margin-bottom: var(--spacing-md);
}

/* ===== PROJECT MODAL STYLES ===== */
.modal-content {
    border: none;
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-heavy);
}

.modal-header {
    background: var(--bg-secondary);
    border-bottom: 1px solid var(--border-color);
    border-radius: var(--radius-lg) var(--radius-lg) 0 0;
}

.modal-title {
    color: var(--text-primary);
    font-weight: 600;
}

.modal-body {
    background: var(--bg-primary);
    padding: var(--spacing-xl);
}

.modal-project-image {
    border-radius: var(--radius-md);
    box-shadow: var(--shadow-light);
}

.project-modal-content h4 {
    color: var(--text-primary);
    margin-bottom: var(--spacing-md);
    font-weight: 600;
}

.project-modal-content p {
    color: var(--text-secondary);
    line-height: 1.6;
    margin-bottom: var(--spacing-lg);
}

.modal-technologies h6 {
    color: var(--text-primary);
    font-weight: 600;
    margin-bottom: var(--spacing-sm);
}

.modal-actions {
    display: flex;
    gap: var(--spacing-md);
    flex-wrap: wrap;
}

/* ===== RESPONSIVE DESIGN FOR PROJECTS ===== */
@media (max-width: 768px) {
    .project-filters {
        flex-direction: column;
        align-items: center;
    }

    .filter-btn {
        width: 200px;
        margin-bottom: var(--spacing-sm);
    }

    .stats-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
    }

    .project-actions {
        gap: var(--spacing-sm);
    }

    .project-action-btn {
        width: 40px;
        height: 40px;
        font-size: var(--font-size-base);
    }

    .modal-actions {
        justify-content: center;
    }
}

/* ===== CONTACT PAGE STYLES ===== */
.social-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-xl);
    margin-top: var(--spacing-2xl);
}

.social-card {
    background: var(--bg-secondary);
    padding: var(--spacing-xl);
    border-radius: var(--radius-lg);
    border: 1px solid var(--border-color);
    text-align: center;
    text-decoration: none;
    transition: all var(--transition-normal);
    display: block;
}

.social-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-heavy);
    text-decoration: none;
}

.social-icon {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto var(--spacing-lg);
    font-size: var(--font-size-2xl);
    color: white;
    transition: all var(--transition-fast);
}

.social-icon.linkedin {
    background: #0077b5;
}

.social-icon.github {
    background: #333;
}

.social-icon.email {
    background: var(--primary-color);
}

.social-icon.phone {
    background: var(--success-color);
}

.social-card:hover .social-icon {
    transform: scale(1.1);
}

.social-card h4 {
    color: var(--text-primary);
    margin-bottom: var(--spacing-sm);
    font-weight: 600;
}

.social-card p {
    color: var(--text-secondary);
    margin: 0;
    font-size: var(--font-size-sm);
}

/* ===== ACCORDION STYLES ===== */
.accordion-item {
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    margin-bottom: var(--spacing-md);
}

.accordion-button {
    background: var(--bg-secondary);
    color: var(--text-primary);
    border: none;
    padding: var(--spacing-lg);
    font-weight: 600;
    border-radius: var(--radius-md);
}

.accordion-button:not(.collapsed) {
    background: var(--primary-color);
    color: white;
    box-shadow: none;
}

.accordion-button:focus {
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
    border-color: var(--primary-color);
}

.accordion-button::after {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%23ffffff'%3e%3cpath fill-rule='evenodd' d='M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z'/%3e%3c/svg%3e");
}

.accordion-button.collapsed::after {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%23666'%3e%3cpath fill-rule='evenodd' d='M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z'/%3e%3c/svg%3e");
}

.accordion-body {
    background: var(--bg-primary);
    color: var(--text-secondary);
    padding: var(--spacing-lg);
    line-height: 1.6;
    border-radius: 0 0 var(--radius-md) var(--radius-md);
}

/* ===== RESPONSIVE DESIGN FOR CONTACT ===== */
@media (max-width: 768px) {
    .contact-info {
        grid-template-columns: 1fr;
    }

    .social-grid {
        grid-template-columns: 1fr;
    }

    .social-icon {
        width: 60px;
        height: 60px;
        font-size: var(--font-size-xl);
    }

    .accordion-button {
        padding: var(--spacing-md);
        font-size: var(--font-size-sm);
    }

    .accordion-body {
        padding: var(--spacing-md);
    }
}

/* ===== PRINT STYLES ===== */
@media print {
    .navbar-custom,
    .footer-custom,
    .back-to-top {
        display: none !important;
    }

    .main-content {
        margin-top: 0 !important;
    }

    * {
        background: white !important;
        color: black !important;
        box-shadow: none !important;
    }
}

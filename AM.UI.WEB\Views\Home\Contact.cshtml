@model AM.ApplicationCore.Models.ContactViewModel
@{
    ViewData["Title"] = "Contact - Ikram Segni";
}

<!-- Contact Hero Section -->
<section id="contact-hero" class="section pt-5">
    <div class="container">
        <div class="text-center mb-5">
            <h1 class="section-title">Get In Touch</h1>
            <p class="section-subtitle">
                I'm always excited to discuss new opportunities, projects, or just have a chat about technology. 
                Feel free to reach out!
            </p>
        </div>
    </div>
</section>

<!-- Contact Information -->
<section id="contact-info" class="section pt-0">
    <div class="container">
        <div class="contact-info">
            <div class="contact-item fade-in-up">
                <div class="contact-icon">
                    <i class="fas fa-envelope"></i>
                </div>
                <h4 class="contact-title">Email</h4>
                <div class="contact-details">
                    <a href="mailto:<EMAIL>" class="contact-link">
                        ikramseg<PERSON><EMAIL>
                    </a>
                </div>
            </div>
            
            <div class="contact-item fade-in-up">
                <div class="contact-icon">
                    <i class="fas fa-phone"></i>
                </div>
                <h4 class="contact-title">Phone</h4>
                <div class="contact-details">
                    <a href="tel:+21656588173" class="contact-link">
                        +216 56 588 173
                    </a>
                </div>
            </div>
            
            <div class="contact-item fade-in-up">
                <div class="contact-icon">
                    <i class="fas fa-map-marker-alt"></i>
                </div>
                <h4 class="contact-title">Location</h4>
                <div class="contact-details">
                    Ariana, Tunisia
                </div>
            </div>
            
            <div class="contact-item fade-in-up">
                <div class="contact-icon">
                    <i class="fab fa-linkedin"></i>
                </div>
                <h4 class="contact-title">LinkedIn</h4>
                <div class="contact-details">
                    <a href="https://linkedin.com/in/ikram-segni/" target="_blank" class="contact-link">
                        linkedin.com/in/ikram-segni/
                    </a>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Contact Form Section -->
<section id="contact-form-section" class="section bg-light">
    <div class="container">
        <div class="row">
            <div class="col-lg-8 mx-auto">
                <div class="text-center mb-5">
                    <h2 class="section-title">Send Me a Message</h2>
                    <p class="section-subtitle">
                        Have a project in mind or want to discuss opportunities? I'd love to hear from you!
                    </p>
                </div>
                
                @if (TempData["SuccessMessage"] != null)
                {
                    <div class="alert alert-success">
                        <i class="fas fa-check-circle"></i>
                        @TempData["SuccessMessage"]
                    </div>
                }
                
                @if (!ViewData.ModelState.IsValid)
                {
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-circle"></i>
                        Please correct the errors below and try again.
                    </div>
                }
                
                <form id="contactForm" asp-controller="Contact" asp-action="Send" method="post" class="contact-form fade-in-up">
                    @Html.AntiForgeryToken()
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label asp-for="Name" class="form-label">Full Name *</label>
                                <input asp-for="Name" class="form-control @(Html.ViewData.ModelState["Name"]?.Errors.Count > 0 ? "is-invalid" : "")" 
                                       placeholder="Enter your full name" required>
                                <span asp-validation-for="Name" class="invalid-feedback"></span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label asp-for="Email" class="form-label">Email Address *</label>
                                <input asp-for="Email" type="email" class="form-control @(Html.ViewData.ModelState["Email"]?.Errors.Count > 0 ? "is-invalid" : "")" 
                                       placeholder="Enter your email address" required>
                                <span asp-validation-for="Email" class="invalid-feedback"></span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label asp-for="Subject" class="form-label">Subject *</label>
                        <input asp-for="Subject" class="form-control @(Html.ViewData.ModelState["Subject"]?.Errors.Count > 0 ? "is-invalid" : "")" 
                               placeholder="What's this about?" required>
                        <span asp-validation-for="Subject" class="invalid-feedback"></span>
                    </div>
                    
                    <div class="form-group">
                        <label asp-for="Message" class="form-label">Message *</label>
                        <textarea asp-for="Message" rows="6" class="form-control @(Html.ViewData.ModelState["Message"]?.Errors.Count > 0 ? "is-invalid" : "")" 
                                  placeholder="Tell me about your project or opportunity..." required></textarea>
                        <span asp-validation-for="Message" class="invalid-feedback"></span>
                    </div>
                    
                    <div class="text-center">
                        <button type="submit" class="btn-submit">
                            <i class="fas fa-paper-plane"></i>
                            Send Message
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</section>

<!-- Social Links Section -->
<section id="social-links" class="section">
    <div class="container">
        <div class="text-center mb-5">
            <h2 class="section-title">Connect With Me</h2>
            <p class="section-subtitle">
                Follow me on social media and professional networks
            </p>
        </div>
        
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="social-grid">
                    <a href="https://linkedin.com/in/ikram-segni/" target="_blank" class="social-card fade-in-up">
                        <div class="social-icon linkedin">
                            <i class="fab fa-linkedin-in"></i>
                        </div>
                        <h4>LinkedIn</h4>
                        <p>Professional networking and career updates</p>
                    </a>
                    
                    <a href="https://github.com/ikraaam28" target="_blank" class="social-card fade-in-up">
                        <div class="social-icon github">
                            <i class="fab fa-github"></i>
                        </div>
                        <h4>GitHub</h4>
                        <p>Code repositories and open source contributions</p>
                    </a>
                    
                    <a href="mailto:<EMAIL>" class="social-card fade-in-up">
                        <div class="social-icon email">
                            <i class="fas fa-envelope"></i>
                        </div>
                        <h4>Email</h4>
                        <p>Direct communication for opportunities</p>
                    </a>
                    
                    <a href="tel:+21656588173" class="social-card fade-in-up">
                        <div class="social-icon phone">
                            <i class="fas fa-phone"></i>
                        </div>
                        <h4>Phone</h4>
                        <p>Quick calls and urgent matters</p>
                    </a>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- FAQ Section -->
<section id="faq" class="section bg-light">
    <div class="container">
        <div class="text-center mb-5">
            <h2 class="section-title">Frequently Asked Questions</h2>
            <p class="section-subtitle">
                Common questions about working with me
            </p>
        </div>
        
        <div class="row">
            <div class="col-lg-8 mx-auto">
                <div class="accordion" id="faqAccordion">
                    <div class="accordion-item fade-in-up">
                        <h2 class="accordion-header" id="faq1">
                            <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#collapse1">
                                What type of internships am I looking for?
                            </button>
                        </h2>
                        <div id="collapse1" class="accordion-collapse collapse show" data-bs-parent="#faqAccordion">
                            <div class="accordion-body">
                                I'm seeking 2-month summer internships in web development, particularly in full-stack development 
                                using technologies like .NET Core, Angular, React, or similar modern frameworks. I'm open to 
                                both frontend and backend focused roles.
                            </div>
                        </div>
                    </div>
                    
                    <div class="accordion-item fade-in-up">
                        <h2 class="accordion-header" id="faq2">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapse2">
                                What technologies do I specialize in?
                            </button>
                        </h2>
                        <div id="collapse2" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                            <div class="accordion-body">
                                My core technologies include .NET Core, Angular, React.js, Node.js, Symfony, Java, and various 
                                databases like MySQL and MongoDB. I'm also experienced with mobile development using Android Studio 
                                and Flutter.
                            </div>
                        </div>
                    </div>
                    
                    <div class="accordion-item fade-in-up">
                        <h2 class="accordion-header" id="faq3">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapse3">
                                How quickly can I start a project?
                            </button>
                        </h2>
                        <div id="collapse3" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                            <div class="accordion-body">
                                I'm currently available and can start immediately for internships or projects. My schedule is 
                                flexible, and I'm committed to delivering quality work within agreed timelines.
                            </div>
                        </div>
                    </div>
                    
                    <div class="accordion-item fade-in-up">
                        <h2 class="accordion-header" id="faq4">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapse4">
                                Do I work on remote projects?
                            </button>
                        </h2>
                        <div id="collapse4" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                            <div class="accordion-body">
                                Yes, I'm comfortable working remotely and have experience collaborating with international teams. 
                                I'm also open to on-site opportunities, especially in Tunisia or France where I have previous 
                                experience.
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Call to Action -->
<section id="contact-cta" class="section bg-gradient">
    <div class="container">
        <div class="row">
            <div class="col-lg-8 mx-auto text-center">
                <h2 class="text-white mb-4">Ready to Start a Conversation?</h2>
                <p class="text-white-50 mb-4 lead">
                    Whether you have a project in mind, an internship opportunity, or just want to connect, 
                    I'm always happy to chat about technology and potential collaborations.
                </p>
                <div class="cta-buttons">
                    <a href="mailto:<EMAIL>" class="btn btn-light btn-lg me-3">
                        <i class="fas fa-envelope"></i>
                        Send Email
                    </a>
                    <a asp-controller="Home" asp-action="DownloadCV" class="btn btn-outline-light btn-lg">
                        <i class="fas fa-download"></i>
                        Download CV
                    </a>
                </div>
            </div>
        </div>
    </div>
</section>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
    
    <script>
        // Enhanced form submission with better UX
        document.getElementById('contactForm').addEventListener('submit', function(e) {
            const submitBtn = this.querySelector('.btn-submit');
            const originalText = submitBtn.innerHTML;
            
            // Show loading state
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Sending...';
            
            // Reset button after 10 seconds if no response (fallback)
            setTimeout(() => {
                if (submitBtn.disabled) {
                    submitBtn.disabled = false;
                    submitBtn.innerHTML = originalText;
                }
            }, 10000);
        });
        
        // Auto-hide alerts after 5 seconds
        document.addEventListener('DOMContentLoaded', function() {
            const alerts = document.querySelectorAll('.alert');
            alerts.forEach(alert => {
                setTimeout(() => {
                    alert.style.opacity = '0';
                    setTimeout(() => {
                        alert.remove();
                    }, 300);
                }, 5000);
            });
        });
    </script>
}

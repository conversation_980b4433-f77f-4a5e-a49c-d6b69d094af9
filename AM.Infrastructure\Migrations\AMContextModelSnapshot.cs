﻿// <auto-generated />
using System;
using AM.Infrastructure;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;

#nullable disable

namespace AM.Infrastructure.Migrations
{
    [DbContext(typeof(AMContext))]
    partial class AMContextModelSnapshot : ModelSnapshot
    {
        protected override void BuildModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "9.0.2")
                .HasAnnotation("Relational:MaxIdentifierLength", 128);

            SqlServerModelBuilderExtensions.UseIdentityColumns(modelBuilder);

            modelBuilder.Entity("AM.ApplicationCore.Domain.Bouquet", b =>
                {
                    b.Property<int>("BouquetCode")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("BouquetCode"));

                    b.Property<string>("AccompagningMessage")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("BouquetType")
                        .HasColumnType("int");

                    b.Property<DateTime>("CreationDate")
                        .HasColumnType("date");

                    b.Property<string>("CustomerFk")
                        .HasColumnType("nvarchar(450)");

                    b.HasKey("BouquetCode");

                    b.HasIndex("CustomerFk");

                    b.ToTable("Bouquets");
                });

            modelBuilder.Entity("AM.ApplicationCore.Domain.Customer", b =>
                {
                    b.Property<string>("Cin")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("Emailaddress")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("firstname")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("lastname")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("phonenumber")
                        .IsRequired()
                        .HasMaxLength(8)
                        .HasColumnType("nvarchar(8)");

                    b.HasKey("Cin");

                    b.ToTable("Customer");
                });

            modelBuilder.Entity("AM.ApplicationCore.Domain.Flower", b =>
                {
                    b.Property<int>("FlowerId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("FlowerId"));

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<float>("Price")
                        .HasColumnType("real");

                    b.Property<int?>("bouqetFK")
                        .HasColumnType("int");

                    b.Property<string>("color")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("FlowerId");

                    b.HasIndex("bouqetFK");

                    b.ToTable("Flowers");

                    b.UseTptMappingStrategy();
                });

            modelBuilder.Entity("AM.ApplicationCore.Domain.ArtificialFlower", b =>
                {
                    b.HasBaseType("AM.ApplicationCore.Domain.Flower");

                    b.Property<DateTime>("ManufactureDate")
                        .HasColumnType("date");

                    b.Property<string>("Material")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.ToTable("ArtificialFlower", (string)null);
                });

            modelBuilder.Entity("AM.ApplicationCore.Domain.NaturalFlower", b =>
                {
                    b.HasBaseType("AM.ApplicationCore.Domain.Flower");

                    b.Property<bool>("Savage")
                        .HasColumnType("bit");

                    b.Property<int>("Season")
                        .HasColumnType("int");

                    b.Property<string>("origin")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.ToTable("NaturalFlower", (string)null);
                });

            modelBuilder.Entity("AM.ApplicationCore.Domain.Bouquet", b =>
                {
                    b.HasOne("AM.ApplicationCore.Domain.Customer", "Customer")
                        .WithMany("Bouquets")
                        .HasForeignKey("CustomerFk");

                    b.Navigation("Customer");
                });

            modelBuilder.Entity("AM.ApplicationCore.Domain.Flower", b =>
                {
                    b.HasOne("AM.ApplicationCore.Domain.Bouquet", "bouquet")
                        .WithMany("Flowers")
                        .HasForeignKey("bouqetFK");

                    b.Navigation("bouquet");
                });

            modelBuilder.Entity("AM.ApplicationCore.Domain.ArtificialFlower", b =>
                {
                    b.HasOne("AM.ApplicationCore.Domain.Flower", null)
                        .WithOne()
                        .HasForeignKey("AM.ApplicationCore.Domain.ArtificialFlower", "FlowerId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("AM.ApplicationCore.Domain.NaturalFlower", b =>
                {
                    b.HasOne("AM.ApplicationCore.Domain.Flower", null)
                        .WithOne()
                        .HasForeignKey("AM.ApplicationCore.Domain.NaturalFlower", "FlowerId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("AM.ApplicationCore.Domain.Bouquet", b =>
                {
                    b.Navigation("Flowers");
                });

            modelBuilder.Entity("AM.ApplicationCore.Domain.Customer", b =>
                {
                    b.Navigation("Bouquets");
                });
#pragma warning restore 612, 618
        }
    }
}

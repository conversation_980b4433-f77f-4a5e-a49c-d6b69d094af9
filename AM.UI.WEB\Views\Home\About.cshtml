@model AM.UI.WEB.Controllers.AboutViewModel
@{
    ViewData["Title"] = "About - Ikram Segni";
}

<!-- About Hero Section -->
<section id="about-hero" class="section pt-5">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-6 fade-in-left">
                <h1 class="section-title text-start">About Me</h1>
                <p class="lead">
                    I'm <PERSON><PERSON><PERSON>, a passionate 4th-year engineering student at ESPRIT, specializing in 
                    Web and Internet Technologies. My journey in software development has been driven by 
                    curiosity, innovation, and a desire to create meaningful digital solutions.
                </p>
                <p>
                    Based in Ariana, Tunisia, I've had the privilege of working on diverse projects across 
                    different industries, from legal technology to fitness coaching platforms. Each experience 
                    has shaped my understanding of how technology can solve real-world problems.
                </p>
                <div class="personal-info">
                    <div class="row">
                        <div class="col-md-6">
                            <ul class="info-list">
                                <li><strong>Location:</strong> Ariana, Tunisia</li>
                                <li><strong>Email:</strong> <EMAIL></li>
                                <li><strong>Phone:</strong> +216 56 588 173</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <ul class="info-list">
                                <li><strong>Languages:</strong> Arabic, French, English (B2)</li>
                                <li><strong>Status:</strong> Available for Internships</li>
                                <li><strong>Interests:</strong> Photography, Volunteer Work</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-6 fade-in-right">
                <div class="about-image-container">
                    <img src="~/images/about-photo.jpg" alt="Ikram Segni" 
                         class="about-photo img-fluid"
                         onerror="this.src='https://via.placeholder.com/500x600/2563eb/ffffff?text=Ikram+Segni'">
                    <div class="about-decoration"></div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Skills Section -->
<section id="skills" class="section bg-light">
    <div class="container">
        <div class="text-center mb-5">
            <h2 class="section-title">Technical Skills</h2>
            <p class="section-subtitle">
                Technologies and tools I work with to bring ideas to life
            </p>
        </div>
        
        <div class="skills-grid">
            @{
                var skillCategories = Model.Skills.GroupBy(s => s.Category).ToList();
            }
            
            @foreach (var category in skillCategories)
            {
                <div class="skill-category fade-in-up">
                    <h4>
                        <i class="fas fa-@(GetCategoryIcon(category.Key))"></i>
                        @category.Key
                    </h4>
                    @foreach (var skill in category)
                    {
                        <div class="skill-item" data-level="@skill.ProficiencyLevel">
                            <div class="skill-info">
                                <i class="skill-icon @skill.IconClass"></i>
                                <span class="skill-name">@skill.Name</span>
                            </div>
                            <div class="skill-level">
                                @for (int i = 1; i <= 5; i++)
                                {
                                    <div class="skill-dot @(i <= skill.ProficiencyLevel ? "active" : "")"></div>
                                }
                            </div>
                        </div>
                    }
                </div>
            }
        </div>
    </div>
</section>

<!-- Experience Section -->
<section id="experience" class="section">
    <div class="container">
        <div class="text-center mb-5">
            <h2 class="section-title">Professional Experience</h2>
            <p class="section-subtitle">
                My journey through various internships and projects
            </p>
        </div>
        
        <div class="experience-timeline">
            @foreach (var experience in Model.Experiences)
            {
                <div class="experience-item fade-in-up">
                    <div class="experience-date">
                        @experience.StartDate.ToString("MMM yyyy") - 
                        @(experience.EndDate?.ToString("MMM yyyy") ?? "Present")
                    </div>
                    <div class="experience-content">
                        <h3 class="experience-title">@experience.Title</h3>
                        <div class="experience-company">@experience.Company</div>
                        <div class="experience-location">
                            <i class="fas fa-map-marker-alt"></i>
                            @experience.Location
                        </div>
                        <p class="experience-description">@experience.Description</p>
                        
                        @if (experience.Achievements.Any())
                        {
                            <ul class="experience-achievements">
                                @foreach (var achievement in experience.Achievements)
                                {
                                    <li>@achievement</li>
                                }
                            </ul>
                        }
                        
                        @if (experience.Technologies.Any())
                        {
                            <div class="experience-technologies">
                                @foreach (var tech in experience.Technologies)
                                {
                                    <span class="tech-tag">@tech</span>
                                }
                            </div>
                        }
                    </div>
                </div>
            }
        </div>
    </div>
</section>

<!-- Education Section -->
<section id="education" class="section bg-light">
    <div class="container">
        <div class="text-center mb-5">
            <h2 class="section-title">Education</h2>
            <p class="section-subtitle">
                My academic journey and qualifications
            </p>
        </div>
        
        <div class="row">
            @foreach (var education in Model.Education)
            {
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="education-card fade-in-up">
                        <div class="education-icon">
                            <i class="fas fa-graduation-cap"></i>
                        </div>
                        <h4 class="education-degree">@education.Degree</h4>
                        <h5 class="education-institution">@education.Institution</h5>
                        <div class="education-location">
                            <i class="fas fa-map-marker-alt"></i>
                            @education.Location
                        </div>
                        <div class="education-date">
                            @education.StartDate.ToString("yyyy") - 
                            @(education.EndDate?.ToString("yyyy") ?? "Present")
                        </div>
                        @if (!string.IsNullOrEmpty(education.Description))
                        {
                            <p class="education-description">@education.Description</p>
                        }
                    </div>
                </div>
            }
        </div>
    </div>
</section>

<!-- Volunteer Work Section -->
<section id="volunteer" class="section">
    <div class="container">
        <div class="text-center mb-5">
            <h2 class="section-title">Community Involvement</h2>
            <p class="section-subtitle">
                Giving back to the community through volunteer work
            </p>
        </div>
        
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="volunteer-card fade-in-up">
                    <div class="volunteer-icon">
                        <i class="fas fa-heart"></i>
                    </div>
                    <h4>Humanitarian Aid Volunteer</h4>
                    <h5>Croissant-Rouge (Red Crescent)</h5>
                    <div class="volunteer-date">2017</div>
                    <p class="volunteer-description">
                        Dedicated 24 hours to humanitarian aid activities with the Tunisian Red Crescent, 
                        contributing to community welfare and emergency response initiatives. This experience 
                        reinforced my commitment to social responsibility and helping others.
                    </p>
                    <div class="volunteer-impact">
                        <div class="impact-item">
                            <strong>24</strong>
                            <span>Hours Volunteered</span>
                        </div>
                        <div class="impact-item">
                            <strong>Community</strong>
                            <span>Service Focus</span>
                        </div>
                        <div class="impact-item">
                            <strong>2017</strong>
                            <span>Year of Service</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Call to Action -->
<section id="about-cta" class="section bg-gradient">
    <div class="container">
        <div class="row">
            <div class="col-lg-8 mx-auto text-center">
                <h2 class="text-white mb-4">Let's Create Something Amazing Together</h2>
                <p class="text-white-50 mb-4 lead">
                    I'm passionate about technology and always eager to take on new challenges. 
                    Whether it's an internship opportunity or an exciting project, I'd love to hear from you!
                </p>
                <div class="cta-buttons">
                    <a asp-controller="Home" asp-action="Contact" class="btn btn-light btn-lg me-3">
                        <i class="fas fa-envelope"></i>
                        Get In Touch
                    </a>
                    <a asp-controller="Home" asp-action="Projects" class="btn btn-outline-light btn-lg">
                        <i class="fas fa-code"></i>
                        View My Projects
                    </a>
                </div>
            </div>
        </div>
    </div>
</section>

@functions {
    string GetCategoryIcon(string category)
    {
        return category switch
        {
            "Programming Languages" => "code",
            "Frontend" => "palette",
            "Backend" => "server",
            "Databases" => "database",
            "Mobile" => "mobile-alt",
            "Tools" => "tools",
            "Systems" => "desktop",
            _ => "cog"
        };
    }
}

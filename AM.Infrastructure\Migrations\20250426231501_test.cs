﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace AM.Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class test : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "agriculteurs",
                columns: table => new
                {
                    CIN = table.Column<string>(type: "nvarchar(450)", nullable: false),
                    Password = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    PrenomNom = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    DateNaissance = table.Column<DateTime>(type: "date", nullable: false),
                    Email = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    Telephone = table.Column<string>(type: "nvarchar(max)", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_agriculteurs", x => x.CIN);
                });

            migrationBuilder.CreateTable(
                name: "Cooperative",
                columns: table => new
                {
                    Reference = table.Column<string>(type: "nvarchar(10)", maxLength: 10, nullable: false),
                    Password = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    Telephone = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    Email = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    Description = table.Column<string>(type: "nvarchar(max)", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Cooperative", x => x.Reference);
                });

            migrationBuilder.CreateTable(
                name: "Parcelles",
                columns: table => new
                {
                    Reference = table.Column<string>(type: "nvarchar(10)", maxLength: 10, nullable: false),
                    Superficie = table.Column<double>(type: "float", nullable: false),
                    Localisation = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    CooperativeRef = table.Column<string>(type: "nvarchar(10)", nullable: false),
                    Sol = table.Column<int>(type: "int", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Parcelles", x => x.Reference);
                    table.ForeignKey(
                        name: "FK_Parcelles_Cooperative_CooperativeRef",
                        column: x => x.CooperativeRef,
                        principalTable: "Cooperative",
                        principalColumn: "Reference",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "Agricultures",
                columns: table => new
                {
                    ParcelleRef = table.Column<string>(type: "nvarchar(10)", nullable: false),
                    AgriculteurCIN = table.Column<string>(type: "nvarchar(450)", nullable: false),
                    DatePlantation = table.Column<DateTime>(type: "date", nullable: false),
                    Description = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    DateRecolte = table.Column<DateTime>(type: "date", nullable: false),
                    PrixLocationParcelle = table.Column<double>(type: "float", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Agricultures", x => new { x.ParcelleRef, x.AgriculteurCIN, x.DatePlantation });
                    table.ForeignKey(
                        name: "FK_Agricultures_Parcelles_ParcelleRef",
                        column: x => x.ParcelleRef,
                        principalTable: "Parcelles",
                        principalColumn: "Reference",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_Agricultures_agriculteurs_AgriculteurCIN",
                        column: x => x.AgriculteurCIN,
                        principalTable: "agriculteurs",
                        principalColumn: "CIN",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_Agricultures_AgriculteurCIN",
                table: "Agricultures",
                column: "AgriculteurCIN");

            migrationBuilder.CreateIndex(
                name: "IX_Parcelles_CooperativeRef",
                table: "Parcelles",
                column: "CooperativeRef");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "Agricultures");

            migrationBuilder.DropTable(
                name: "Parcelles");

            migrationBuilder.DropTable(
                name: "agriculteurs");

            migrationBuilder.DropTable(
                name: "Cooperative");
        }
    }
}

﻿using System;
using AM.ApplicationCore.Domain;
using AM.ApplicationCore;
using AM.ApplicationCore.Services;
using System.Numerics;
using AM.Infrastructure;
using Microsoft.Extensions.Options;
using Microsoft.EntityFrameworkCore;
class Program
{
    static void Main()
    {

        //Plane plane1 = new Plane();
        //plane1.PlaneId = 1;
        //plane1.PlaneType = PlaneType.Airbus;
        //plane1.Capacity = 180;
        //plane1.ManufactureDate = new DateTime(2018, 3, 15);
        //Console.WriteLine("Avion 1 (Initialisation avec constructeur non paramétré) :");
        //Console.WriteLine(plane1);

        //Plane plane2 = new Plane(PlaneType.Boeing, 220, new DateTime(2019, 7, 20));
        //Console.WriteLine(plane2);


        //Plane plane3 = new Plane
        //{
        //    PlaneId = 3,
        //    PlaneType = PlaneType.Boeing,
        //    Capacity = 200,
        //    ManufactureDate = new DateTime(2025, 5, 25)
        //};


        //Passenger p1 = new Passenger { FirstName = "ikram", LastName = "segni", Email = "<EMAIL>" };
        //Staff s1 = new Staff { FirstName = "ikram", LastName = "segni", Email = "<EMAIL>"  };
        //Traveller t1 = new Traveller { FirstName = "ikram", LastName = "segni", Email = "<EMAIL>" };


        //Console.WriteLine(p1.CheckProfile("ikram", "segni")); 
        //Console.WriteLine(p1.CheckProfile("ikram", "segni", "<EMAIL>")); 
        //Console.WriteLine(p1.CheckProfile("ikram", "segni")); 

        //Console.WriteLine(p1.PassengerType());
        //Console.WriteLine(s1.PassengerType());
        //Console.WriteLine(t1.PassengerType()); 

        //   Console.WriteLine("Liste des vols disponibles : ");
        //   foreach (var flight in fm.GetFlightDates("Paris"))
        //   {
        //       Console.WriteLine(flight);
        //   }
        //   Console.WriteLine("Liste des vols disponibles 2 : ");

        //  foreach (var flight in fm.GetFlights("Destination", "Paris"))
        //   {
        //       Console.WriteLine(flight);
        //   }


        //   Console.WriteLine("e langage LINQ");
        // foreach (var date in fm.GetFlightDates("Paris"))
        //   {
        //       Console.WriteLine(date);
        //   }

        //   var fd = fm.ShowFlightDetails(TestData.Airbusplane);
        //foreach (var flight in fd)
        //       {
        //           Console.WriteLine($"Date: {flight.FlightDate}, Destination: {flight.Destination}");
        //       }


        //Console.WriteLine("Nombre de vols programmés sur une semaine à partir d'une date donnée :");
        //Console.WriteLine(fm.ProgrammedFlightNumber(new DateTime(2022, 01, 01)));

        //Console.WriteLine("Moyenne de durée des vols vers Paris :");
        //Console.WriteLine(fm.DurationAverage("Paris"));

        //Console.WriteLine("Vols triés par durée estimée (du plus long au plus court) :");
        //foreach (var flight in fm.OrderedDurationFlights())
        //{
        //    Console.WriteLine(flight);
        //}

        //Console.WriteLine("Les 3 passagers les plus âgés du vol 1 :");
        //foreach (var traveller in fm.SeniorTravellers(TestData.flight1))
        //{
        //    Console.WriteLine(traveller);
        //}

        //Console.WriteLine("Liste des vols groupés par destination :");
        //fm.DestinationGroupedFlights();


        //Passenger p = new P

        //insertion dans la base
        AMContext context = new AMContext();

        //Console.WriteLine("ajout avec success");

        //foreach (var fl in context.Flights)
        //    Console.WriteLine(fl.Destination);
        //Console.WriteLine(context.Flights.First().Plane.Capacity);
        // Ajouter les avions (les entités Plane)
     
        // Ajouter les passagers (staffs + travellers)
        //context.Passengers.Add(TestData.captain);
        //context.Passengers.Add(TestData.hostess1);
        //context.Passengers.Add(TestData.hostess2);
        //context.Passengers.Add(TestData.traveller1);
        //context.Passengers.Add(TestData.traveller2);
        //context.Passengers.Add(TestData.traveller3);
        //context.Passengers.Add(TestData.traveller4);
        //context.Passengers.Add(TestData.traveller5);

        // Ajouter les vols

        // Sauvegarder les modifications dans la base
        context.SaveChanges();





    }



}

@model List<AM.UI.WEB.Models.ProjectViewModel>
@{
    ViewData["Title"] = "Projects - Ikram Segni";
}

<!-- Projects Hero Section -->
<section id="projects-hero" class="section pt-5">
    <div class="container">
        <div class="text-center mb-5">
            <h1 class="section-title">My Projects</h1>
            <p class="section-subtitle">
                A showcase of my technical skills and creative solutions across various domains
            </p>
        </div>
    </div>
</section>

<!-- Project Filters -->
<section id="project-filters" class="section pt-0">
    <div class="container">
        <div class="project-filters">
            <button class="filter-btn active" data-filter="all">
                <i class="fas fa-th"></i>
                All Projects
            </button>
            <button class="filter-btn" data-filter="Full Stack">
                <i class="fas fa-layer-group"></i>
                Full Stack
            </button>
            <button class="filter-btn" data-filter="Web Application">
                <i class="fas fa-globe"></i>
                Web Apps
            </button>
            <button class="filter-btn" data-filter="Enterprise">
                <i class="fas fa-building"></i>
                Enterprise
            </button>
            <button class="filter-btn" data-filter="Mobile">
                <i class="fas fa-mobile-alt"></i>
                Mobile
            </button>
        </div>
    </div>
</section>

<!-- Projects Grid -->
<section id="projects-grid" class="section pt-0">
    <div class="container">
        <div class="projects-grid">
            @foreach (var project in Model)
            {
                <div class="project-card fade-in-up" data-category="@project.Category">
                    @if (project.IsFeatured)
                    {
                        <div class="featured-badge">
                            <i class="fas fa-star"></i>
                            Featured
                        </div>
                    }
                    
                    <div class="project-image-container">
                        <img src="@project.ImageUrl" alt="@project.Title" class="project-image"
                             onerror="this.src='https://via.placeholder.com/400x250/@(project.IsFeatured ? "f59e0b" : "2563eb")/ffffff?text=@Uri.EscapeDataString(project.Title)'">
                        <div class="project-overlay">
                            <div class="project-actions">
                                @if (!string.IsNullOrEmpty(project.GitHubUrl))
                                {
                                    <a href="@project.GitHubUrl" target="_blank" class="project-action-btn" title="View Code">
                                        <i class="fab fa-github"></i>
                                    </a>
                                }
                                @if (!string.IsNullOrEmpty(project.LiveDemoUrl))
                                {
                                    <a href="@project.LiveDemoUrl" target="_blank" class="project-action-btn" title="Live Demo">
                                        <i class="fas fa-external-link-alt"></i>
                                    </a>
                                }
                                <button class="project-action-btn" onclick="openProjectModal('@project.Title.Replace("'", "\\'")','@project.Description.Replace("'", "\\'")','@string.Join(",", project.Technologies)','@project.ImageUrl','@project.GitHubUrl','@project.LiveDemoUrl')" title="View Details">
                                    <i class="fas fa-info-circle"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    <div class="project-content">
                        <div class="project-meta">
                            <span class="project-category">@project.Category</span>
                            <span class="project-date">@project.Date.ToString("MMM yyyy")</span>
                        </div>
                        
                        <h3 class="project-title">@project.Title</h3>
                        <p class="project-description">@project.Description</p>
                        
                        <div class="project-technologies">
                            @foreach (var tech in project.Technologies)
                            {
                                <span class="tech-tag">@tech</span>
                            }
                        </div>
                        
                        <div class="project-links">
                            @if (!string.IsNullOrEmpty(project.GitHubUrl))
                            {
                                <a href="@project.GitHubUrl" target="_blank" class="project-link">
                                    <i class="fab fa-github"></i>
                                    Code
                                </a>
                            }
                            @if (!string.IsNullOrEmpty(project.LiveDemoUrl))
                            {
                                <a href="@project.LiveDemoUrl" target="_blank" class="project-link">
                                    <i class="fas fa-external-link-alt"></i>
                                    Demo
                                </a>
                            }
                        </div>
                    </div>
                </div>
            }
        </div>
        
        @if (!Model.Any())
        {
            <div class="text-center py-5">
                <div class="empty-state">
                    <i class="fas fa-code fa-3x text-muted mb-3"></i>
                    <h4>No Projects Found</h4>
                    <p class="text-muted">Projects will be displayed here once they are added.</p>
                </div>
            </div>
        }
    </div>
</section>

<!-- GitHub Integration Section -->
<section id="github-stats" class="section bg-light">
    <div class="container">
        <div class="text-center mb-5">
            <h2 class="section-title">GitHub Activity</h2>
            <p class="section-subtitle">
                Check out my latest contributions and repositories
            </p>
        </div>
        
        <div class="row">
            <div class="col-lg-6 mb-4">
                <div class="github-card fade-in-left">
                    <div class="github-header">
                        <i class="fab fa-github"></i>
                        <h4>GitHub Profile</h4>
                    </div>
                    <div class="github-content">
                        <p>Follow my coding journey and explore my repositories on GitHub.</p>
                        <a href="https://github.com/ikraaam28" target="_blank" class="btn-primary-custom">
                            <i class="fab fa-github"></i>
                            Visit GitHub Profile
                        </a>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-6 mb-4">
                <div class="github-card fade-in-right">
                    <div class="github-header">
                        <i class="fas fa-chart-line"></i>
                        <h4>Contribution Stats</h4>
                    </div>
                    <div class="github-content">
                        <div class="stats-grid">
                            <div class="stat-item">
                                <strong>5+</strong>
                                <span>Public Repos</span>
                            </div>
                            <div class="stat-item">
                                <strong>15+</strong>
                                <span>Technologies</span>
                            </div>
                            <div class="stat-item">
                                <strong>Active</strong>
                                <span>Contributor</span>
                            </div>
                            <div class="stat-item">
                                <strong>2024</strong>
                                <span>Latest Activity</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Call to Action -->
<section id="projects-cta" class="section bg-gradient">
    <div class="container">
        <div class="row">
            <div class="col-lg-8 mx-auto text-center">
                <h2 class="text-white mb-4">Interested in Collaborating?</h2>
                <p class="text-white-50 mb-4 lead">
                    I'm always excited to work on new projects and learn from experienced developers. 
                    Let's build something amazing together!
                </p>
                <div class="cta-buttons">
                    <a asp-controller="Home" asp-action="Contact" class="btn btn-light btn-lg me-3">
                        <i class="fas fa-envelope"></i>
                        Start a Conversation
                    </a>
                    <a asp-controller="Home" asp-action="About" class="btn btn-outline-light btn-lg">
                        <i class="fas fa-user"></i>
                        Learn More About Me
                    </a>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Project Modal -->
<div class="modal fade" id="projectModal" tabindex="-1" aria-labelledby="projectModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="projectModalLabel">Project Details</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="project-modal-content">
                    <img id="modalProjectImage" src="" alt="" class="modal-project-image img-fluid mb-3">
                    <h4 id="modalProjectTitle"></h4>
                    <p id="modalProjectDescription"></p>
                    <div class="modal-technologies mb-3">
                        <h6>Technologies Used:</h6>
                        <div id="modalProjectTechnologies"></div>
                    </div>
                    <div class="modal-actions">
                        <a id="modalGitHubLink" href="#" target="_blank" class="btn btn-outline-primary me-2" style="display: none;">
                            <i class="fab fa-github"></i>
                            View Code
                        </a>
                        <a id="modalDemoLink" href="#" target="_blank" class="btn btn-primary" style="display: none;">
                            <i class="fas fa-external-link-alt"></i>
                            Live Demo
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        // Project modal functionality
        function openProjectModal(title, description, technologies, imageUrl, githubUrl, demoUrl) {
            document.getElementById('modalProjectTitle').textContent = title;
            document.getElementById('modalProjectDescription').textContent = description;
            document.getElementById('modalProjectImage').src = imageUrl;
            
            // Technologies
            const techContainer = document.getElementById('modalProjectTechnologies');
            techContainer.innerHTML = '';
            technologies.split(',').forEach(tech => {
                const span = document.createElement('span');
                span.className = 'tech-tag me-2 mb-2';
                span.textContent = tech.trim();
                techContainer.appendChild(span);
            });
            
            // Links
            const githubLink = document.getElementById('modalGitHubLink');
            const demoLink = document.getElementById('modalDemoLink');
            
            if (githubUrl) {
                githubLink.href = githubUrl;
                githubLink.style.display = 'inline-block';
            } else {
                githubLink.style.display = 'none';
            }
            
            if (demoUrl) {
                demoLink.href = demoUrl;
                demoLink.style.display = 'inline-block';
            } else {
                demoLink.style.display = 'none';
            }
            
            // Show modal
            const modal = new bootstrap.Modal(document.getElementById('projectModal'));
            modal.show();
        }
        
        // Initialize project filters
        document.addEventListener('DOMContentLoaded', function() {
            const filterBtns = document.querySelectorAll('.filter-btn');
            const projectCards = document.querySelectorAll('.project-card');
            
            filterBtns.forEach(btn => {
                btn.addEventListener('click', function() {
                    const filter = this.getAttribute('data-filter');
                    
                    // Update active button
                    filterBtns.forEach(b => b.classList.remove('active'));
                    this.classList.add('active');
                    
                    // Filter projects with animation
                    projectCards.forEach(card => {
                        const category = card.getAttribute('data-category');
                        if (filter === 'all' || category === filter) {
                            card.style.display = 'block';
                            setTimeout(() => {
                                card.style.opacity = '1';
                                card.style.transform = 'scale(1)';
                            }, 100);
                        } else {
                            card.style.opacity = '0';
                            card.style.transform = 'scale(0.8)';
                            setTimeout(() => {
                                card.style.display = 'none';
                            }, 300);
                        }
                    });
                });
            });
        });
    </script>
}

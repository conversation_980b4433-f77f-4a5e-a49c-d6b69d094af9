@{
    ViewData["Title"] = "Ikram Segni - Full Stack Developer";
}

<!-- Hero Section -->
<section id="home" class="hero-section">
    <div class="container">
        <div class="row align-items-center min-vh-100">
            <div class="col-lg-6">
                <div class="hero-content">
                    <h1 class="hero-title">
                        Hi, I'm <span class="text-gradient">Ikram Segni</span>
                    </h1>
                    <h2 class="hero-subtitle">
                        <span class="typing-text">Full Stack Developer</span>
                    </h2>
                    <p class="hero-description">
                        Passionate engineering student specializing in web technologies and internet systems. 
                        I create innovative digital solutions using modern frameworks like .NET Core, Angular, 
                        React, and more. Currently seeking internship opportunities to contribute to exciting projects.
                    </p>
                    <div class="hero-buttons">
                        <a href="#projects" class="btn-primary-custom">
                            <i class="fas fa-code"></i>
                            View My Work
                        </a>
                        <a asp-controller="Home" asp-action="Contact" class="btn-secondary-custom">
                            <i class="fas fa-envelope"></i>
                            Get In Touch
                        </a>
                    </div>
                </div>
            </div>
            <div class="col-lg-6">
                <div class="hero-image text-center">
                    <img src="~/images/profile.jpg" alt="Ikram Segni - Full Stack Developer" 
                         class="profile-image img-fluid" 
                         onerror="this.src='https://via.placeholder.com/400x400/2563eb/ffffff?text=Ikram+Segni'">
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Quick Stats Section -->
<section id="stats" class="section bg-light">
    <div class="container">
        <div class="row">
            <div class="col-lg-3 col-md-6 mb-4">
                <div class="stat-card text-center fade-in-up">
                    <div class="stat-icon">
                        <i class="fas fa-code"></i>
                    </div>
                    <h3 class="stat-number" data-count="15">0</h3>
                    <p class="stat-label">Technologies Mastered</p>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-4">
                <div class="stat-card text-center fade-in-up">
                    <div class="stat-icon">
                        <i class="fas fa-project-diagram"></i>
                    </div>
                    <h3 class="stat-number" data-count="5">0</h3>
                    <p class="stat-label">Projects Completed</p>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-4">
                <div class="stat-card text-center fade-in-up">
                    <div class="stat-icon">
                        <i class="fas fa-briefcase"></i>
                    </div>
                    <h3 class="stat-number" data-count="4">0</h3>
                    <p class="stat-label">Internships</p>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-4">
                <div class="stat-card text-center fade-in-up">
                    <div class="stat-icon">
                        <i class="fas fa-graduation-cap"></i>
                    </div>
                    <h3 class="stat-number" data-count="4">0</h3>
                    <p class="stat-label">Years of Study</p>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- About Preview Section -->
<section id="about-preview" class="section">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-6 fade-in-left">
                <h2 class="section-title text-start">About Me</h2>
                <p class="lead">
                    I'm a 4th-year engineering student at ESPRIT, specializing in Web and Internet Technologies. 
                    My journey in software development has taken me through various exciting projects and internships 
                    across Tunisia and France.
                </p>
                <p>
                    From developing debt recovery systems for LegalTech to creating innovative learning platforms 
                    with AI integration, I'm passionate about solving real-world problems through technology.
                </p>
                <div class="skills-preview">
                    <h5>Core Technologies:</h5>
                    <div class="tech-tags">
                        <span class="tech-tag">.NET Core</span>
                        <span class="tech-tag">Angular</span>
                        <span class="tech-tag">React.js</span>
                        <span class="tech-tag">Node.js</span>
                        <span class="tech-tag">Symfony</span>
                        <span class="tech-tag">MongoDB</span>
                        <span class="tech-tag">MySQL</span>
                    </div>
                </div>
                <div class="mt-4">
                    <a asp-controller="Home" asp-action="About" class="btn-primary-custom">
                        <i class="fas fa-user"></i>
                        Learn More About Me
                    </a>
                </div>
            </div>
            <div class="col-lg-6 fade-in-right">
                <div class="about-image">
                    <img src="~/images/about-illustration.svg" alt="About Ikram Segni" 
                         class="img-fluid"
                         onerror="this.src='https://via.placeholder.com/500x400/f8fafc/2563eb?text=About+Me'">
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Featured Projects Section -->
<section id="projects-preview" class="section bg-light">
    <div class="container">
        <div class="text-center mb-5">
            <h2 class="section-title">Featured Projects</h2>
            <p class="section-subtitle">
                Here are some of my recent projects that showcase my skills and passion for development
            </p>
        </div>
        <div class="row">
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="project-card-preview fade-in-up">
                    <div class="project-image-preview">
                        <img src="~/images/projects/bootcamp-platform.jpg" alt="Bootcamp Platform" 
                             class="img-fluid"
                             onerror="this.src='https://via.placeholder.com/350x200/2563eb/ffffff?text=Bootcamp+Platform'">
                        <div class="project-overlay">
                            <a href="#" class="project-link-preview">
                                <i class="fas fa-external-link-alt"></i>
                            </a>
                        </div>
                    </div>
                    <div class="project-content-preview">
                        <h4>Bootcamp Learning Platform</h4>
                        <p>Full-stack application with AI-powered face recognition for assessments</p>
                        <div class="project-tech-preview">
                            <span class="tech-tag">React.js</span>
                            <span class="tech-tag">Node.js</span>
                            <span class="tech-tag">MongoDB</span>
                            <span class="tech-tag">OpenCV</span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="project-card-preview fade-in-up">
                    <div class="project-image-preview">
                        <img src="~/images/projects/alpha-fit.jpg" alt="Alpha-Fit Platform" 
                             class="img-fluid"
                             onerror="this.src='https://via.placeholder.com/350x200/2563eb/ffffff?text=Alpha-Fit'">
                        <div class="project-overlay">
                            <a href="#" class="project-link-preview">
                                <i class="fas fa-external-link-alt"></i>
                            </a>
                        </div>
                    </div>
                    <div class="project-content-preview">
                        <h4>Alpha-Fit Coaching Platform</h4>
                        <p>Web and desktop application for fitness coaching and program management</p>
                        <div class="project-tech-preview">
                            <span class="tech-tag">Symfony</span>
                            <span class="tech-tag">Java</span>
                            <span class="tech-tag">JavaFx</span>
                            <span class="tech-tag">MySQL</span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="project-card-preview fade-in-up">
                    <div class="project-image-preview">
                        <img src="~/images/projects/legaltech.jpg" alt="LegalTech System" 
                             class="img-fluid"
                             onerror="this.src='https://via.placeholder.com/350x200/2563eb/ffffff?text=LegalTech'">
                        <div class="project-overlay">
                            <a href="#" class="project-link-preview">
                                <i class="fas fa-external-link-alt"></i>
                            </a>
                        </div>
                    </div>
                    <div class="project-content-preview">
                        <h4>Debt Recovery System</h4>
                        <p>Legal workflow management system for banking debt recovery processes</p>
                        <div class="project-tech-preview">
                            <span class="tech-tag">Angular</span>
                            <span class="tech-tag">Symfony</span>
                            <span class="tech-tag">MySQL</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="text-center mt-4">
            <a asp-controller="Home" asp-action="Projects" class="btn-primary-custom">
                <i class="fas fa-code"></i>
                View All Projects
            </a>
        </div>
    </div>
</section>

<!-- Call to Action Section -->
<section id="cta" class="section bg-gradient">
    <div class="container">
        <div class="row">
            <div class="col-lg-8 mx-auto text-center">
                <h2 class="text-white mb-4">Ready to Work Together?</h2>
                <p class="text-white-50 mb-4 lead">
                    I'm currently seeking internship opportunities and exciting projects. 
                    Let's discuss how we can create something amazing together!
                </p>
                <div class="cta-buttons">
                    <a asp-controller="Home" asp-action="Contact" class="btn btn-light btn-lg me-3">
                        <i class="fas fa-envelope"></i>
                        Contact Me
                    </a>
                    <a asp-controller="Home" asp-action="DownloadCV" class="btn btn-outline-light btn-lg">
                        <i class="fas fa-download"></i>
                        Download CV
                    </a>
                </div>
            </div>
        </div>
    </div>
</section>

@section Scripts {
    <script>
        // Counter animation for stats
        document.addEventListener('DOMContentLoaded', function() {
            const counters = document.querySelectorAll('.stat-number');
            
            const animateCounter = (counter) => {
                const target = parseInt(counter.getAttribute('data-count'));
                const increment = target / 50;
                let current = 0;
                
                const timer = setInterval(() => {
                    current += increment;
                    if (current >= target) {
                        counter.textContent = target;
                        clearInterval(timer);
                    } else {
                        counter.textContent = Math.floor(current);
                    }
                }, 50);
            };
            
            // Intersection Observer for counter animation
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const counter = entry.target.querySelector('.stat-number');
                        if (counter && !counter.classList.contains('animated')) {
                            counter.classList.add('animated');
                            animateCounter(counter);
                        }
                    }
                });
            }, { threshold: 0.5 });
            
            document.querySelectorAll('.stat-card').forEach(card => {
                observer.observe(card);
            });
        });
    </script>
}

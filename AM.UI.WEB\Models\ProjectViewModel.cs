namespace AM.UI.WEB.Models
{
    public class ProjectViewModel
    {
        public string Title { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string ImageUrl { get; set; } = string.Empty;
        public List<string> Technologies { get; set; } = new();
        public string? GitHubUrl { get; set; }
        public string? LiveDemoUrl { get; set; }
        public string Category { get; set; } = string.Empty;
        public DateTime Date { get; set; }
        public bool IsFeatured { get; set; }
    }

    public class SkillViewModel
    {
        public string Name { get; set; } = string.Empty;
        public string Category { get; set; } = string.Empty;
        public int ProficiencyLevel { get; set; } // 1-5 scale
        public string IconClass { get; set; } = string.Empty;
    }

    public class ExperienceViewModel
    {
        public string Title { get; set; } = string.Empty;
        public string Company { get; set; } = string.Empty;
        public string Location { get; set; } = string.Empty;
        public DateTime StartDate { get; set; }
        public DateTime? EndDate { get; set; }
        public string Description { get; set; } = string.Empty;
        public List<string> Achievements { get; set; } = new();
        public List<string> Technologies { get; set; } = new();
        public bool IsCurrent => EndDate == null;
    }

    public class EducationViewModel
    {
        public string Degree { get; set; } = string.Empty;
        public string Institution { get; set; } = string.Empty;
        public string Location { get; set; } = string.Empty;
        public DateTime StartDate { get; set; }
        public DateTime? EndDate { get; set; }
        public string? Description { get; set; }
    }
}

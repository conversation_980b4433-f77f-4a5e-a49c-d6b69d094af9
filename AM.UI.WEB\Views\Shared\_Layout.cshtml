<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>@ViewData["Title"] - AM.UI.WEB</title>
    <link rel="stylesheet" href="~/lib/bootstrap/dist/css/bootstrap.min.css" />
    <link rel="stylesheet" href="~/css/site.css" asp-append-version="true" />
    <link rel="stylesheet" href="~/AM.UI.WEB.styles.css" asp-append-version="true" />
</head>
<body>
    <header>
        <nav class="navbar navbar-expand-sm navbar-toggleable-sm navbar-light bg-white border-bottom box-shadow mb-3">
            <div class="container-fluid">
                <a class="navbar-brand" asp-area="" asp-controller="Home" asp-action="Index">AM.UI.WEB</a>
                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target=".navbar-collapse" aria-controls="navbarSupportedContent"
                        aria-expanded="false" aria-label="Toggle navigation">
                    <span class="navbar-toggler-icon"></span>
                </button>
                <div class="navbar-collapse collapse d-sm-inline-flex justify-content-between">
                    <ul class="navbar-nav flex-grow-1">
                        <li class="nav-item">
                            <a class="nav-link text-dark" asp-area="" asp-controller="Home" asp-action="Index">Home</a>
                        </li>
                  SEGNI IKRAM
Élève ingénieure en Technologies du Web et de l’Internet
[ <EMAIL> Ó +21656588173 ½ Ariana, Tunisie ¯ linkedin.com/in/ikram-segni/
 github.com/ikraaam28
EXPÉRIENCES PROFESSIONNELLES
Stage d’immersion en entreprise
PROGEDEV, Bordeaux, France
 Juin 2024 - Août 2024
Développement d’une application Web pour la société PROGEDEV et
conception d’interfaces Figma pour un projet destiné à la gendarmerie
• Développement de fonctionnalités spécifiques pour les besoins internes de
l’entreprise.
• Création de maquettes et prototypes d’interfaces avec Figma.
• Participation à un projet de digitalisation destiné aux services de gendarmerie.
Stage de fin d’étude
LegalTech, Pôle Urbain Nord, Tunis
 Février 2023 - Juin 2023
Réalisation d’une application de recouvrement amiable et judiciaire des
créances bancaires
• Développement de l’application avec Angular (frontend) et Symfony (backend).
• Mise en place de workflows juridiques pour le traitement des créances.
• Intégration de la base de données et gestion des droits d’accès.
Stage de perfectionnement en développement Web
Innov’up, Tunis
 Juillet 2022 - Août 2022
Conception et réalisation d’une application Web pour la gestion des recettes municipales
• Réalisation des interfaces utilisateurs avec Angular et Bootstrap.
• Traitement des données au format JSON.
• Optimisation des performances et ergonomie de l’application.
Stage d’initiation en développement mobile
Smart Tunisian Technoparks, Tunis
 Janvier 2021 - Février 2021
Conception et réalisation des interfaces d’une application mobile de
restauration
• Utilisation de Android Studio pour le développement mobile.
• Création des maquettes avec Adobe XD.
• Implémentation des interfaces avec XML.
PROJETS ACADÉMIQUES
3
PI Full stack JS : Application Web pour un centre de formation bootcamp avec certificats crédibles, un apprentissage interactif, et une
intégration d’IA pour la reconnaissance faciale lors des évaluations
ReactJS | Nodejs | MongoDB | OpenCV
3
PIDEV 2024 : Développement d’une application web et desktop de
coaching «Alpha-Fit»
Symfony | Java | JavaFx | SceneBuilder | MySQL
PROFIL
Étudiante en 4ème année en ingénierie des technologies du web à ESPRIT, je suis à la recherche
d’un stage d’été de 2 mois. Autonome, adaptable
et motivée, je m’intègre facilement en équipe et
souhaite mettre mes compétences au service de
projets concrets.
• Langues : Arabe , Français, Anglais (B2 - certifié TOEIC)
EDUCATION
Diplôme d’ingénieur Informatique
Ecole Supérieure Privée d’Ingénierie et de
Technologies
 Sept 2023 – Présent ½ Tunis, Tunisie
Licence En Développement Des Systèmes
D’Information
Institut Supérieur Des Etudes Technologique De
Kelibia
 Sept 2020 – Juin 2023 ½ Kelibia, Tunisie
Baccalauréat en Science expérimentale
Lycée Hannibal Ariana
 Juin 2020 ½ Ariana, Tunisie
COMPÉTENCES
Langages de programmation
HTML | CSS | Java | Java Script | SQL | PHP |
Python | C# |
Frameworks
Flutter | Django | Symfony | Angular | ReactJS |
.NET | Spring Boot | Laravel
Base de données
MySql | Mongodb | Oracle
Systèmes d’exploitation
Windows | Linux
VIE ASSOCIATIVE
• 24 heures de bénévolat – Aide humanitaire au sein du
Croissant-Rouge.2017
INTÉRÊTS
♥ Travail bénévole
 Photograp
                  
                    </ul>
                </div>
            </div>
        </nav>
    </header>
    <div class="container">
        <main role="main" class="pb-3">
            @RenderBody()
        </main>
    </div>

    <footer class="border-top footer text-muted">
        <div class="container">
            &copy; 2025 - AM.UI.WEB - <a asp-area="" asp-controller="Home" asp-action="Privacy">Privacy</a>
        </div>
    </footer>
    <script src="~/lib/jquery/dist/jquery.min.js"></script>
    <script src="~/lib/bootstrap/dist/js/bootstrap.bundle.min.js"></script>
    <script src="~/js/site.js" asp-append-version="true"></script>
    @await RenderSectionAsync("Scripts", required: false)
</body>
</html>

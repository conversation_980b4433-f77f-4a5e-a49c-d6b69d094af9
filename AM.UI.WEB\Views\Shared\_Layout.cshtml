<!DOCTYPE html>
<html lang="en" data-theme="light">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>@ViewData["Title"]</title>
    <meta name="description" content="Ikram Segni - Full Stack Web Developer specializing in .NET, Angular, React, and modern web technologies. Available for internships and projects." />
    <meta name="keywords" content="Full Stack Developer, .NET Core, Angular, React, Web Development, Ikram Segni" />
    <meta name="author" content="Ikram Segni" />

    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website" />
    <meta property="og:title" content="@ViewData["Title"]" />
    <meta property="og:description" content="Full Stack Web Developer specializing in modern web technologies" />
    <meta property="og:url" content="@($"{Context.Request.Scheme}://{Context.Request.Host}{Context.Request.Path}")" />

    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image" />
    <meta property="twitter:title" content="@ViewData["Title"]" />
    <meta property="twitter:description" content="Full Stack Web Developer specializing in modern web technologies" />

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="~/favicon.ico" />

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" />

    <!-- Styles -->
    <link rel="stylesheet" href="~/lib/bootstrap/dist/css/bootstrap.min.css" />
    <link rel="stylesheet" href="~/css/portfolio.css" asp-append-version="true" />
    <link rel="stylesheet" href="~/css/site.css" asp-append-version="true" />
</head>
<body>
    <!-- Loading Screen -->
    <div id="loading-screen" class="loading-screen">
        <div class="loading-spinner">
            <div class="spinner"></div>
            <p>Loading...</p>
        </div>
    </div>

    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-custom fixed-top" id="mainNav">
        <div class="container">
            <a class="navbar-brand" asp-controller="Home" asp-action="Index">
                <span class="brand-text">Ikram Segni</span>
            </a>

            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon">
                    <i class="fas fa-bars"></i>
                </span>
            </button>

            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" asp-controller="Home" asp-action="Index">
                            <i class="fas fa-home"></i> Home
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" asp-controller="Home" asp-action="About">
                            <i class="fas fa-user"></i> About
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" asp-controller="Home" asp-action="Projects">
                            <i class="fas fa-code"></i> Projects
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" asp-controller="Home" asp-action="Contact">
                            <i class="fas fa-envelope"></i> Contact
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link btn-download" asp-controller="Home" asp-action="DownloadCV">
                            <i class="fas fa-download"></i> CV
                        </a>
                    </li>
                    <li class="nav-item">
                        <button class="theme-toggle" id="themeToggle" title="Toggle theme">
                            <i class="fas fa-moon"></i>
                        </button>
                    </li>
                </ul>
            </div>
        </div>
    </nav>
    <!-- Main Content -->
    <main class="main-content">
        @RenderBody()
    </main>

    <!-- Footer -->
    <footer class="footer-custom">
        <div class="container">
            <div class="row">
                <div class="col-lg-6">
                    <div class="footer-info">
                        <h5>Ikram Segni</h5>
                        <p>Full Stack Web Developer passionate about creating innovative solutions with modern technologies.</p>
                        <div class="social-links">
                            <a href="mailto:<EMAIL>" title="Email">
                                <i class="fas fa-envelope"></i>
                            </a>
                            <a href="https://linkedin.com/in/ikram-segni/" target="_blank" title="LinkedIn">
                                <i class="fab fa-linkedin"></i>
                            </a>
                            <a href="https://github.com/ikraaam28" target="_blank" title="GitHub">
                                <i class="fab fa-github"></i>
                            </a>
                            <a href="tel:+21656588173" title="Phone">
                                <i class="fas fa-phone"></i>
                            </a>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3">
                    <div class="footer-links">
                        <h6>Quick Links</h6>
                        <ul>
                            <li><a asp-controller="Home" asp-action="Index">Home</a></li>
                            <li><a asp-controller="Home" asp-action="About">About</a></li>
                            <li><a asp-controller="Home" asp-action="Projects">Projects</a></li>
                            <li><a asp-controller="Home" asp-action="Contact">Contact</a></li>
                        </ul>
                    </div>
                </div>
                <div class="col-lg-3">
                    <div class="footer-contact">
                        <h6>Contact Info</h6>
                        <p><i class="fas fa-map-marker-alt"></i> Ariana, Tunisia</p>
                        <p><i class="fas fa-envelope"></i> <EMAIL></p>
                        <p><i class="fas fa-phone"></i> +216 56 588 173</p>
                    </div>
                </div>
            </div>
            <hr class="footer-divider">
            <div class="row">
                <div class="col-12 text-center">
                    <p class="footer-copyright">
                        &copy; @DateTime.Now.Year Ikram Segni. All rights reserved.
                        Built with <i class="fas fa-heart text-danger"></i> using .NET Core MVC
                    </p>
                </div>
            </div>
        </div>
    </footer>

    <!-- Back to Top Button -->
    <button class="back-to-top" id="backToTop" title="Back to top">
        <i class="fas fa-chevron-up"></i>
    </button>

    <!-- Scripts -->
    <script src="~/lib/jquery/dist/jquery.min.js"></script>
    <script src="~/lib/bootstrap/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/aos/2.3.4/aos.js"></script>
    <script src="~/js/portfolio.js" asp-append-version="true"></script>
    <script src="~/js/site.js" asp-append-version="true"></script>

    @await RenderSectionAsync("Scripts", required: false)
</body>
</html>

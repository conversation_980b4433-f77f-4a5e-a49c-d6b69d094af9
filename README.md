# Ikram Segni - Portfolio Website

A modern, responsive portfolio website built with .NET Core MVC showcasing the skills, projects, and experience of <PERSON><PERSON><PERSON>gni, a Full Stack Web Developer.

## 🚀 Features

### Core Functionality
- **Responsive Design**: Mobile-first approach with modern UI/UX
- **Dark/Light Theme**: Toggle between themes with persistent storage
- **Contact Form**: Functional contact form with email integration
- **CV Download**: Direct PDF download functionality
- **Project Showcase**: Interactive project gallery with filtering
- **Skills Display**: Animated skill proficiency indicators
- **Experience Timeline**: Professional experience with detailed descriptions

### Technical Features
- **Modern CSS**: CSS Grid, Flexbox, CSS Variables, and smooth animations
- **Interactive JavaScript**: Smooth scrolling, lazy loading, and dynamic content
- **SEO Optimized**: Meta tags, structured data, and semantic HTML
- **Performance Optimized**: Lazy loading, caching, and optimized assets
- **Accessibility**: ARIA labels, keyboard navigation, and screen reader support

## 🛠️ Technology Stack

### Backend
- **.NET Core 8.0**: Modern web framework
- **Entity Framework Core**: Database ORM
- **Clean Architecture**: Separation of concerns with ApplicationCore and Infrastructure layers

### Frontend
- **HTML5 & CSS3**: Modern semantic markup and styling
- **Bootstrap 5**: Responsive grid system and components
- **JavaScript ES6+**: Modern JavaScript features and APIs
- **Font Awesome**: Icon library
- **Google Fonts**: Typography (Inter font family)

### Database
- **SQL Server**: Primary database (LocalDB for development)
- **Entity Framework Migrations**: Database schema management

## 📁 Project Structure

```
AM.UI.WEB/
├── Controllers/
│   ├── HomeController.cs          # Main portfolio controller
│   └── ContactController.cs       # Contact form handling
├── Models/
│   ├── ContactViewModel.cs        # Contact form model
│   ├── ProjectViewModel.cs        # Project data models
│   └── ErrorViewModel.cs          # Error handling
├── Views/
│   ├── Home/
│   │   ├── Index.cshtml           # Homepage
│   │   ├── About.cshtml           # About page
│   │   ├── Projects.cshtml        # Projects gallery
│   │   └── Contact.cshtml         # Contact page
│   └── Shared/
│       └── _Layout.cshtml         # Main layout template
├── wwwroot/
│   ├── css/
│   │   └── portfolio.css          # Custom styles
│   ├── js/
│   │   └── portfolio.js           # Interactive features
│   ├── images/                    # Image assets
│   └── files/                     # Downloadable files (CV)
└── Program.cs                     # Application configuration

AM.ApplicationCore/
├── Interfaces/
│   └── IEmailService.cs           # Email service interface
└── Services/
    └── EmailService.cs            # Email implementation

AM.Infrastructure/
├── AMContext.cs                   # Database context
├── GenericRepository.cs           # Generic repository pattern
└── UnitOfWork.cs                  # Unit of work pattern
```

## 🎨 Design System

### Color Palette
- **Primary**: #2563eb (Blue)
- **Secondary**: #1e40af (Dark Blue)
- **Accent**: #3b82f6 (Light Blue)
- **Success**: #10b981 (Green)
- **Warning**: #f59e0b (Orange)
- **Danger**: #ef4444 (Red)

### Typography
- **Font Family**: Inter (Google Fonts)
- **Responsive Sizing**: Clamp functions for fluid typography
- **Font Weights**: 300, 400, 500, 600, 700

### Spacing System
- **Base Unit**: 1rem (16px)
- **Scale**: 0.25rem, 0.5rem, 1rem, 1.5rem, 2rem, 3rem

## 📧 Email Configuration

To enable the contact form functionality:

1. Update `appsettings.json` with your SMTP settings:
```json
{
  "SmtpSettings": {
    "Host": "smtp.gmail.com",
    "Port": 587,
    "Username": "<EMAIL>",
    "Password": "your-app-password",
    "EnableSsl": true,
    "FromEmail": "<EMAIL>",
    "ToEmail": "<EMAIL>"
  }
}
```

2. For Gmail, use an App Password instead of your regular password
3. Enable 2-factor authentication and generate an App Password

## 🚀 Getting Started

### Prerequisites
- .NET 8.0 SDK
- SQL Server or SQL Server Express
- Visual Studio 2022 or VS Code

### Installation

1. **Clone the repository**
```bash
git clone <repository-url>
cd portfo_ikram
```

2. **Restore packages**
```bash
dotnet restore
```

3. **Update database connection string** in `appsettings.json`

4. **Run database migrations**
```bash
dotnet ef database update
```

5. **Add your CV file**
   - Place your CV as `Ikram_Segni_CV.pdf` in `AM.UI.WEB/wwwroot/files/`

6. **Add profile images**
   - Add your profile photo as `profile.jpg` in `AM.UI.WEB/wwwroot/images/`
   - Add project images in `AM.UI.WEB/wwwroot/images/projects/`

7. **Run the application**
```bash
dotnet run --project AM.UI.WEB
```

8. **Open browser** and navigate to `https://localhost:5001`

## 📱 Responsive Breakpoints

- **Mobile**: < 768px
- **Tablet**: 768px - 1024px
- **Desktop**: > 1024px

## 🎯 Performance Features

- **Lazy Loading**: Images load as they enter viewport
- **Caching**: Memory caching for skills and static data
- **Minification**: CSS and JS optimization
- **Compression**: Gzip compression for static files
- **CDN**: External libraries loaded from CDN

## 🔧 Customization

### Adding New Projects
Update the `GetProjects()` method in `HomeController.cs`:

```csharp
new ProjectViewModel
{
    Title = "Your Project Title",
    Description = "Project description",
    Technologies = new List<string> { "Tech1", "Tech2" },
    GitHubUrl = "https://github.com/username/repo",
    Category = "Web Application",
    IsFeatured = true
}
```

### Modifying Skills
Update the `GetSkills()` method in `HomeController.cs` to add or modify skills.

### Changing Theme Colors
Modify CSS variables in `portfolio.css`:

```css
:root {
    --primary-color: #your-color;
    --secondary-color: #your-color;
    /* ... other variables */
}
```

## 📄 License

This project is open source and available under the [MIT License](LICENSE).

## 👤 About Ikram Segni

- **Email**: <EMAIL>
- **LinkedIn**: [linkedin.com/in/ikram-segni/](https://linkedin.com/in/ikram-segni/)
- **GitHub**: [github.com/ikraaam28](https://github.com/ikraaam28)
- **Location**: Ariana, Tunisia

---

Built with ❤️ using .NET Core MVC

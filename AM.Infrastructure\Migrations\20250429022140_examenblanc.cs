﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace AM.Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class examenblanc : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "Agricultures");

            migrationBuilder.DropTable(
                name: "Parcelles");

            migrationBuilder.DropTable(
                name: "agriculteurs");

            migrationBuilder.DropTable(
                name: "Cooperative");

            migrationBuilder.CreateTable(
                name: "Customer",
                columns: table => new
                {
                    Cin = table.Column<string>(type: "nvarchar(450)", nullable: false),
                    firstname = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    phonenumber = table.Column<string>(type: "nvarchar(8)", maxLength: 8, nullable: false),
                    Emailaddress = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    lastname = table.Column<string>(type: "nvarchar(max)", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Customer", x => x.Cin);
                });

            migrationBuilder.CreateTable(
                name: "Bouquets",
                columns: table => new
                {
                    BouquetCode = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    AccompagningMessage = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    BouquetType = table.Column<int>(type: "int", nullable: false),
                    CreationDate = table.Column<DateTime>(type: "date", nullable: false),
                    CustomerFk = table.Column<string>(type: "nvarchar(450)", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Bouquets", x => x.BouquetCode);
                    table.ForeignKey(
                        name: "FK_Bouquets_Customer_CustomerFk",
                        column: x => x.CustomerFk,
                        principalTable: "Customer",
                        principalColumn: "Cin");
                });

            migrationBuilder.CreateTable(
                name: "Flowers",
                columns: table => new
                {
                    FlowerId = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    color = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    Name = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    Price = table.Column<float>(type: "real", nullable: false),
                    bouqetFK = table.Column<int>(type: "int", nullable: true),
                    Discriminator = table.Column<string>(type: "nvarchar(21)", maxLength: 21, nullable: false),
                    ManufactureDate = table.Column<DateTime>(type: "date", nullable: true),
                    Material = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    origin = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Savage = table.Column<bool>(type: "bit", nullable: true),
                    Season = table.Column<int>(type: "int", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Flowers", x => x.FlowerId);
                    table.ForeignKey(
                        name: "FK_Flowers_Bouquets_bouqetFK",
                        column: x => x.bouqetFK,
                        principalTable: "Bouquets",
                        principalColumn: "BouquetCode");
                });

            migrationBuilder.CreateIndex(
                name: "IX_Bouquets_CustomerFk",
                table: "Bouquets",
                column: "CustomerFk");

            migrationBuilder.CreateIndex(
                name: "IX_Flowers_bouqetFK",
                table: "Flowers",
                column: "bouqetFK");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "Flowers");

            migrationBuilder.DropTable(
                name: "Bouquets");

            migrationBuilder.DropTable(
                name: "Customer");

            migrationBuilder.CreateTable(
                name: "agriculteurs",
                columns: table => new
                {
                    CIN = table.Column<string>(type: "nvarchar(450)", nullable: false),
                    DateNaissance = table.Column<DateTime>(type: "date", nullable: false),
                    Email = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    Password = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    PrenomNom = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    Telephone = table.Column<string>(type: "nvarchar(max)", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_agriculteurs", x => x.CIN);
                });

            migrationBuilder.CreateTable(
                name: "Cooperative",
                columns: table => new
                {
                    Reference = table.Column<string>(type: "nvarchar(10)", maxLength: 10, nullable: false),
                    Description = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    Email = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    Password = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    Telephone = table.Column<string>(type: "nvarchar(max)", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Cooperative", x => x.Reference);
                });

            migrationBuilder.CreateTable(
                name: "Parcelles",
                columns: table => new
                {
                    Reference = table.Column<string>(type: "nvarchar(10)", maxLength: 10, nullable: false),
                    CooperativeRef = table.Column<string>(type: "nvarchar(10)", nullable: false),
                    Localisation = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    Sol = table.Column<int>(type: "int", nullable: false),
                    Superficie = table.Column<double>(type: "float", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Parcelles", x => x.Reference);
                    table.ForeignKey(
                        name: "FK_Parcelles_Cooperative_CooperativeRef",
                        column: x => x.CooperativeRef,
                        principalTable: "Cooperative",
                        principalColumn: "Reference",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "Agricultures",
                columns: table => new
                {
                    ParcelleRef = table.Column<string>(type: "nvarchar(10)", nullable: false),
                    AgriculteurCIN = table.Column<string>(type: "nvarchar(450)", nullable: false),
                    DatePlantation = table.Column<DateTime>(type: "date", nullable: false),
                    DateRecolte = table.Column<DateTime>(type: "date", nullable: false),
                    Description = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    PrixLocationParcelle = table.Column<double>(type: "float", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Agricultures", x => new { x.ParcelleRef, x.AgriculteurCIN, x.DatePlantation });
                    table.ForeignKey(
                        name: "FK_Agricultures_Parcelles_ParcelleRef",
                        column: x => x.ParcelleRef,
                        principalTable: "Parcelles",
                        principalColumn: "Reference",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_Agricultures_agriculteurs_AgriculteurCIN",
                        column: x => x.AgriculteurCIN,
                        principalTable: "agriculteurs",
                        principalColumn: "CIN",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_Agricultures_AgriculteurCIN",
                table: "Agricultures",
                column: "AgriculteurCIN");

            migrationBuilder.CreateIndex(
                name: "IX_Parcelles_CooperativeRef",
                table: "Parcelles",
                column: "CooperativeRef");
        }
    }
}

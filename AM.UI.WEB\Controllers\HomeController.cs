using AM.UI.WEB.Models;
using AM.ApplicationCore.Models;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Caching.Memory;
using System.Diagnostics;

namespace AM.UI.WEB.Controllers
{
    public class HomeController : Controller
    {
        private readonly IMemoryCache _cache;
        private readonly ILogger<HomeController> _logger;

        public HomeController(IMemoryCache cache, ILogger<HomeController> logger)
        {
            _cache = cache;
            _logger = logger;
        }

        public IActionResult Index()
        {
            ViewData["Title"] = "Ikram Segni - Full Stack Developer";
            return View();
        }

        public IActionResult About()
        {
            ViewData["Title"] = "About - Ikram Segni";
            
            var model = new AboutViewModel
            {
                Skills = GetSkills(),
                Experiences = GetExperiences(),
                Education = GetEducation()
            };
            
            return View(model);
        }

        public IActionResult Projects()
        {
            ViewData["Title"] = "Projects - Ikram Segni";
            
            var projects = GetProjects();
            return View(projects);
        }

        public IActionResult Contact()
        {
            ViewData["Title"] = "Contact - Ikram Segni";
            return View(new ContactViewModel());
        }

        public IActionResult DownloadCV()
        {
            var filePath = Path.Combine(Directory.GetCurrentDirectory(), "wwwroot", "files", "Ikram_Segni_CV.pdf");
            
            if (System.IO.File.Exists(filePath))
            {
                var fileBytes = System.IO.File.ReadAllBytes(filePath);
                return File(fileBytes, "application/pdf", "Ikram_Segni_CV.pdf");
            }
            
            return NotFound();
        }

        [ResponseCache(Duration = 0, Location = ResponseCacheLocation.None, NoStore = true)]
        public IActionResult Error()
        {
            return View(new ErrorViewModel { RequestId = Activity.Current?.Id ?? HttpContext.TraceIdentifier });
        }

        private List<SkillViewModel> GetSkills()
        {
            return _cache.GetOrCreate("skills", entry =>
            {
                entry.AbsoluteExpirationRelativeToNow = TimeSpan.FromHours(24);
                return new List<SkillViewModel>
                {
                    // Programming Languages
                    new() { Name = "C#", Category = "Programming Languages", ProficiencyLevel = 4, IconClass = "fab fa-microsoft" },
                    new() { Name = "JavaScript", Category = "Programming Languages", ProficiencyLevel = 5, IconClass = "fab fa-js-square" },
                    new() { Name = "Java", Category = "Programming Languages", ProficiencyLevel = 4, IconClass = "fab fa-java" },
                    new() { Name = "Python", Category = "Programming Languages", ProficiencyLevel = 4, IconClass = "fab fa-python" },
                    new() { Name = "PHP", Category = "Programming Languages", ProficiencyLevel = 4, IconClass = "fab fa-php" },
                    new() { Name = "SQL", Category = "Programming Languages", ProficiencyLevel = 4, IconClass = "fas fa-database" },
                    
                    // Frontend Frameworks
                    new() { Name = "React.js", Category = "Frontend", ProficiencyLevel = 5, IconClass = "fab fa-react" },
                    new() { Name = "Angular", Category = "Frontend", ProficiencyLevel = 4, IconClass = "fab fa-angular" },
                    new() { Name = "HTML5", Category = "Frontend", ProficiencyLevel = 5, IconClass = "fab fa-html5" },
                    new() { Name = "CSS3", Category = "Frontend", ProficiencyLevel = 5, IconClass = "fab fa-css3-alt" },
                    new() { Name = "Bootstrap", Category = "Frontend", ProficiencyLevel = 4, IconClass = "fab fa-bootstrap" },
                    
                    // Backend Frameworks
                    new() { Name = ".NET Core", Category = "Backend", ProficiencyLevel = 4, IconClass = "fab fa-microsoft" },
                    new() { Name = "Symfony", Category = "Backend", ProficiencyLevel = 4, IconClass = "fab fa-symfony" },
                    new() { Name = "Laravel", Category = "Backend", ProficiencyLevel = 3, IconClass = "fab fa-laravel" },
                    new() { Name = "Spring Boot", Category = "Backend", ProficiencyLevel = 3, IconClass = "fas fa-leaf" },
                    new() { Name = "Node.js", Category = "Backend", ProficiencyLevel = 4, IconClass = "fab fa-node-js" },
                    new() { Name = "Django", Category = "Backend", ProficiencyLevel = 3, IconClass = "fab fa-python" },
                    
                    // Databases
                    new() { Name = "MySQL", Category = "Databases", ProficiencyLevel = 4, IconClass = "fas fa-database" },
                    new() { Name = "MongoDB", Category = "Databases", ProficiencyLevel = 4, IconClass = "fas fa-leaf" },
                    new() { Name = "Oracle", Category = "Databases", ProficiencyLevel = 3, IconClass = "fas fa-database" },
                    
                    // Mobile & Other
                    new() { Name = "Flutter", Category = "Mobile", ProficiencyLevel = 3, IconClass = "fas fa-mobile-alt" },
                    new() { Name = "Android Studio", Category = "Mobile", ProficiencyLevel = 3, IconClass = "fab fa-android" },
                    new() { Name = "Git", Category = "Tools", ProficiencyLevel = 4, IconClass = "fab fa-git-alt" },
                    new() { Name = "Linux", Category = "Systems", ProficiencyLevel = 3, IconClass = "fab fa-linux" },
                    new() { Name = "Windows", Category = "Systems", ProficiencyLevel = 5, IconClass = "fab fa-windows" }
                };
            });
        }

        private List<ExperienceViewModel> GetExperiences()
        {
            return new List<ExperienceViewModel>
            {
                new()
                {
                    Title = "Stage d'immersion en entreprise",
                    Company = "PROGEDEV",
                    Location = "Bordeaux, France",
                    StartDate = new DateTime(2024, 6, 1),
                    EndDate = new DateTime(2024, 8, 31),
                    Description = "Développement d'une application Web pour la société PROGEDEV et conception d'interfaces Figma pour un projet destiné à la gendarmerie",
                    Achievements = new List<string>
                    {
                        "Développement de fonctionnalités spécifiques pour les besoins internes de l'entreprise",
                        "Création de maquettes et prototypes d'interfaces avec Figma",
                        "Participation à un projet de digitalisation destiné aux services de gendarmerie"
                    },
                    Technologies = new List<string> { "Web Development", "Figma", "UI/UX Design" }
                },
                new()
                {
                    Title = "Stage de fin d'étude",
                    Company = "LegalTech",
                    Location = "Pôle Urbain Nord, Tunis",
                    StartDate = new DateTime(2023, 2, 1),
                    EndDate = new DateTime(2023, 6, 30),
                    Description = "Réalisation d'une application de recouvrement amiable et judiciaire des créances bancaires",
                    Achievements = new List<string>
                    {
                        "Développement de l'application avec Angular (frontend) et Symfony (backend)",
                        "Mise en place de workflows juridiques pour le traitement des créances",
                        "Intégration de la base de données et gestion des droits d'accès"
                    },
                    Technologies = new List<string> { "Angular", "Symfony", "Database Integration", "Access Management" }
                },
                new()
                {
                    Title = "Stage de perfectionnement en développement Web",
                    Company = "Innov'up",
                    Location = "Tunis",
                    StartDate = new DateTime(2022, 7, 1),
                    EndDate = new DateTime(2022, 8, 31),
                    Description = "Conception et réalisation d'une application Web pour la gestion des recettes municipales",
                    Achievements = new List<string>
                    {
                        "Réalisation des interfaces utilisateurs avec Angular et Bootstrap",
                        "Traitement des données au format JSON",
                        "Optimisation des performances et ergonomie de l'application"
                    },
                    Technologies = new List<string> { "Angular", "Bootstrap", "JSON", "Performance Optimization" }
                },
                new()
                {
                    Title = "Stage d'initiation en développement mobile",
                    Company = "Smart Tunisian Technoparks",
                    Location = "Tunis",
                    StartDate = new DateTime(2021, 1, 1),
                    EndDate = new DateTime(2021, 2, 28),
                    Description = "Conception et réalisation des interfaces d'une application mobile de restauration",
                    Achievements = new List<string>
                    {
                        "Utilisation de Android Studio pour le développement mobile",
                        "Création des maquettes avec Adobe XD",
                        "Implémentation des interfaces avec XML"
                    },
                    Technologies = new List<string> { "Android Studio", "Adobe XD", "XML", "Mobile Development" }
                }
            };
        }

        private List<EducationViewModel> GetEducation()
        {
            return new List<EducationViewModel>
            {
                new()
                {
                    Degree = "Diplôme d'ingénieur Informatique",
                    Institution = "Ecole Supérieure Privée d'Ingénierie et de Technologies (ESPRIT)",
                    Location = "Tunis, Tunisie",
                    StartDate = new DateTime(2023, 9, 1),
                    EndDate = null,
                    Description = "Spécialisation en Technologies du Web et de l'Internet"
                },
                new()
                {
                    Degree = "Licence En Développement Des Systèmes D'Information",
                    Institution = "Institut Supérieur Des Etudes Technologique De Kelibia",
                    Location = "Kelibia, Tunisie",
                    StartDate = new DateTime(2020, 9, 1),
                    EndDate = new DateTime(2023, 6, 30)
                },
                new()
                {
                    Degree = "Baccalauréat en Science expérimentale",
                    Institution = "Lycée Hannibal Ariana",
                    Location = "Ariana, Tunisie",
                    StartDate = new DateTime(2019, 9, 1),
                    EndDate = new DateTime(2020, 6, 30)
                }
            };
        }

        private List<ProjectViewModel> GetProjects()
        {
            return new List<ProjectViewModel>
            {
                new()
                {
                    Title = "PI Full stack JS - Bootcamp Learning Platform",
                    Description = "Application Web pour un centre de formation bootcamp avec certificats crédibles, un apprentissage interactif, et une intégration d'IA pour la reconnaissance faciale lors des évaluations",
                    ImageUrl = "/images/projects/bootcamp-platform.jpg",
                    Technologies = new List<string> { "React.js", "Node.js", "MongoDB", "OpenCV", "AI/ML" },
                    GitHubUrl = "https://github.com/ikraaam28/bootcamp-platform",
                    Category = "Full Stack",
                    Date = new DateTime(2024, 1, 1),
                    IsFeatured = true
                },
                new()
                {
                    Title = "PIDEV 2024 - Alpha-Fit Coaching Platform",
                    Description = "Développement d'une application web et desktop de coaching «Alpha-Fit» avec fonctionnalités complètes de gestion des programmes d'entraînement",
                    ImageUrl = "/images/projects/alpha-fit.jpg",
                    Technologies = new List<string> { "Symfony", "Java", "JavaFx", "SceneBuilder", "MySQL" },
                    GitHubUrl = "https://github.com/ikraaam28/alpha-fit",
                    Category = "Full Stack",
                    Date = new DateTime(2024, 1, 1),
                    IsFeatured = true
                },
                new()
                {
                    Title = "LegalTech - Debt Recovery System",
                    Description = "Application de recouvrement amiable et judiciaire des créances bancaires avec workflows juridiques automatisés",
                    ImageUrl = "/images/projects/legaltech.jpg",
                    Technologies = new List<string> { "Angular", "Symfony", "MySQL", "Workflow Management" },
                    Category = "Enterprise",
                    Date = new DateTime(2023, 6, 1),
                    IsFeatured = true
                },
                new()
                {
                    Title = "Municipal Revenue Management System",
                    Description = "Application Web pour la gestion des recettes municipales avec interfaces optimisées et traitement de données JSON",
                    ImageUrl = "/images/projects/municipal-system.jpg",
                    Technologies = new List<string> { "Angular", "Bootstrap", "JSON API", "Performance Optimization" },
                    Category = "Web Application",
                    Date = new DateTime(2022, 8, 1),
                    IsFeatured = false
                },
                new()
                {
                    Title = "Restaurant Mobile App",
                    Description = "Application mobile de restauration avec interfaces utilisateur modernes et expérience utilisateur optimisée",
                    ImageUrl = "/images/projects/restaurant-app.jpg",
                    Technologies = new List<string> { "Android Studio", "XML", "Adobe XD", "Mobile UI/UX" },
                    Category = "Mobile",
                    Date = new DateTime(2021, 2, 1),
                    IsFeatured = false
                }
            };
        }
    }

    public class AboutViewModel
    {
        public List<SkillViewModel> Skills { get; set; } = new();
        public List<ExperienceViewModel> Experiences { get; set; } = new();
        public List<EducationViewModel> Education { get; set; } = new();
    }
}

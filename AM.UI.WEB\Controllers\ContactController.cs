using AM.ApplicationCore.Interfaces;
using AM.ApplicationCore.Models;
using Microsoft.AspNetCore.Mvc;

namespace AM.UI.WEB.Controllers
{
    public class ContactController : Controller
    {
        private readonly IEmailService _emailService;
        private readonly ILogger<ContactController> _logger;

        public ContactController(IEmailService emailService, ILogger<ContactController> logger)
        {
            _emailService = emailService;
            _logger = logger;
        }

        [HttpPost]
        public async Task<IActionResult> Send(ContactViewModel model)
        {
            if (!ModelState.IsValid)
            {
                if (Request.Headers["X-Requested-With"] == "XMLHttpRequest")
                {
                    return Json(new { success = false, message = "Please fill in all required fields correctly." });
                }
                return View("~/Views/Home/Contact.cshtml", model);
            }

            try
            {
                var emailSent = await _emailService.SendContactEmailAsync(model);
                
                if (emailSent)
                {
                    _logger.LogInformation("Contact form submitted successfully by {Email}", model.Email);
                    
                    if (Request.Headers["X-Requested-With"] == "XMLHttpRequest")
                    {
                        return Json(new { success = true, message = "Thank you for your message! I'll get back to you soon." });
                    }
                    
                    TempData["SuccessMessage"] = "Thank you for your message! I'll get back to you soon.";
                    return RedirectToAction("Contact", "Home");
                }
                else
                {
                    _logger.LogError("Failed to send contact form email for {Email}", model.Email);
                    
                    if (Request.Headers["X-Requested-With"] == "XMLHttpRequest")
                    {
                        return Json(new { success = false, message = "Sorry, there was an error sending your message. Please try again later." });
                    }
                    
                    ModelState.AddModelError("", "Sorry, there was an error sending your message. Please try again later.");
                    return View("~/Views/Home/Contact.cshtml", model);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Exception occurred while sending contact form email for {Email}", model.Email);
                
                if (Request.Headers["X-Requested-With"] == "XMLHttpRequest")
                {
                    return Json(new { success = false, message = "Sorry, there was an error sending your message. Please try again later." });
                }
                
                ModelState.AddModelError("", "Sorry, there was an error sending your message. Please try again later.");
                return View("~/Views/Home/Contact.cshtml", model);
            }
        }
    }
}

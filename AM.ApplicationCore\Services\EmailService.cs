using AM.ApplicationCore.Interfaces;
using AM.ApplicationCore.Models;
using Microsoft.Extensions.Options;
using System.Net;
using System.Net.Mail;

namespace AM.ApplicationCore.Services
{
    public class EmailService : IEmailService
    {
        private readonly SmtpSettings _smtpSettings;

        public EmailService(IOptions<SmtpSettings> smtpSettings)
        {
            _smtpSettings = smtpSettings.Value;
        }

        public async Task<bool> SendContactEmailAsync(ContactViewModel contact)
        {
            try
            {
                using var client = new SmtpClient(_smtpSettings.Host, _smtpSettings.Port)
                {
                    Credentials = new NetworkCredential(_smtpSettings.Username, _smtpSettings.Password),
                    EnableSsl = _smtpSettings.EnableSsl
                };

                var mailMessage = new MailMessage
                {
                    From = new MailAddress(_smtpSettings.FromEmail, "Portfolio Contact Form"),
                    Subject = $"Portfolio Contact: {contact.Subject}",
                    Body = $@"
                        <h3>New Contact Form Submission</h3>
                        <p><strong>Name:</strong> {contact.Name}</p>
                        <p><strong>Email:</strong> {contact.Email}</p>
                        <p><strong>Subject:</strong> {contact.Subject}</p>
                        <p><strong>Message:</strong></p>
                        <p>{contact.Message.Replace("\n", "<br>")}</p>
                        <hr>
                        <p><small>Sent from Ikram Segni's Portfolio Website</small></p>
                    ",
                    IsBodyHtml = true
                };

                mailMessage.To.Add(_smtpSettings.ToEmail);
                mailMessage.ReplyToList.Add(contact.Email);

                await client.SendMailAsync(mailMessage);
                return true;
            }
            catch (Exception)
            {
                return false;
            }
        }
    }

    public class SmtpSettings
    {
        public string Host { get; set; } = string.Empty;
        public int Port { get; set; }
        public string Username { get; set; } = string.Empty;
        public string Password { get; set; } = string.Empty;
        public bool EnableSsl { get; set; }
        public string FromEmail { get; set; } = string.Empty;
        public string ToEmail { get; set; } = string.Empty;
    }
}

﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace AM.Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class examenblanc2 : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "Discriminator",
                table: "Flowers");

            migrationBuilder.DropColumn(
                name: "ManufactureDate",
                table: "Flowers");

            migrationBuilder.DropColumn(
                name: "Material",
                table: "Flowers");

            migrationBuilder.DropColumn(
                name: "<PERSON>",
                table: "Flowers");

            migrationBuilder.DropColumn(
                name: "Season",
                table: "Flowers");

            migrationBuilder.DropColumn(
                name: "origin",
                table: "Flowers");

            migrationBuilder.CreateTable(
                name: "ArtificialFlower",
                columns: table => new
                {
                    FlowerId = table.Column<int>(type: "int", nullable: false),
                    ManufactureDate = table.Column<DateTime>(type: "date", nullable: false),
                    Material = table.Column<string>(type: "nvarchar(max)", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ArtificialFlower", x => x.FlowerId);
                    table.ForeignKey(
                        name: "FK_ArtificialFlower_Flowers_FlowerId",
                        column: x => x.FlowerId,
                        principalTable: "Flowers",
                        principalColumn: "FlowerId",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "NaturalFlower",
                columns: table => new
                {
                    FlowerId = table.Column<int>(type: "int", nullable: false),
                    origin = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    Savage = table.Column<bool>(type: "bit", nullable: false),
                    Season = table.Column<int>(type: "int", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_NaturalFlower", x => x.FlowerId);
                    table.ForeignKey(
                        name: "FK_NaturalFlower_Flowers_FlowerId",
                        column: x => x.FlowerId,
                        principalTable: "Flowers",
                        principalColumn: "FlowerId",
                        onDelete: ReferentialAction.Cascade);
                });
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "ArtificialFlower");

            migrationBuilder.DropTable(
                name: "NaturalFlower");

            migrationBuilder.AddColumn<string>(
                name: "Discriminator",
                table: "Flowers",
                type: "nvarchar(21)",
                maxLength: 21,
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<DateTime>(
                name: "ManufactureDate",
                table: "Flowers",
                type: "date",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "Material",
                table: "Flowers",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<bool>(
                name: "Savage",
                table: "Flowers",
                type: "bit",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "Season",
                table: "Flowers",
                type: "int",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "origin",
                table: "Flowers",
                type: "nvarchar(max)",
                nullable: true);
        }
    }
}

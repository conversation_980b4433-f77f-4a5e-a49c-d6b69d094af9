﻿// <auto-generated />
using System;
using AM.Infrastructure;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;

#nullable disable

namespace AM.Infrastructure.Migrations
{
    [DbContext(typeof(AMContext))]
    [Migration("20250426231501_test")]
    partial class test
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "9.0.2")
                .HasAnnotation("Relational:MaxIdentifierLength", 128);

            SqlServerModelBuilderExtensions.UseIdentityColumns(modelBuilder);

            modelBuilder.Entity("AM.ApplicationCore.Domain.Agriculteur", b =>
                {
                    b.Property<string>("CIN")
                        .HasColumnType("nvarchar(450)");

                    b.Property<DateTime>("DateNaissance")
                        .HasColumnType("date");

                    b.Property<string>("Email")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Password")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("PrenomNom")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Telephone")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("CIN");

                    b.ToTable("agriculteurs");
                });

            modelBuilder.Entity("AM.ApplicationCore.Domain.Agriculture", b =>
                {
                    b.Property<string>("ParcelleRef")
                        .HasColumnType("nvarchar(10)");

                    b.Property<string>("AgriculteurCIN")
                        .HasColumnType("nvarchar(450)");

                    b.Property<DateTime>("DatePlantation")
                        .HasColumnType("date");

                    b.Property<DateTime>("DateRecolte")
                        .HasColumnType("date");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<double>("PrixLocationParcelle")
                        .HasColumnType("float");

                    b.HasKey("ParcelleRef", "AgriculteurCIN", "DatePlantation");

                    b.HasIndex("AgriculteurCIN");

                    b.ToTable("Agricultures");
                });

            modelBuilder.Entity("AM.ApplicationCore.Domain.Cooperative", b =>
                {
                    b.Property<string>("Reference")
                        .HasMaxLength(10)
                        .HasColumnType("nvarchar(10)");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Email")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Password")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Telephone")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Reference");

                    b.ToTable("Cooperative");
                });

            modelBuilder.Entity("AM.ApplicationCore.Domain.Parcelle", b =>
                {
                    b.Property<string>("Reference")
                        .HasMaxLength(10)
                        .HasColumnType("nvarchar(10)");

                    b.Property<string>("CooperativeRef")
                        .IsRequired()
                        .HasColumnType("nvarchar(10)");

                    b.Property<string>("Localisation")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("Sol")
                        .HasColumnType("int");

                    b.Property<double>("Superficie")
                        .HasColumnType("float");

                    b.HasKey("Reference");

                    b.HasIndex("CooperativeRef");

                    b.ToTable("Parcelles");
                });

            modelBuilder.Entity("AM.ApplicationCore.Domain.Agriculture", b =>
                {
                    b.HasOne("AM.ApplicationCore.Domain.Agriculteur", "Agriculteur")
                        .WithMany("Agricultures")
                        .HasForeignKey("AgriculteurCIN")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("AM.ApplicationCore.Domain.Parcelle", "Parcelle")
                        .WithMany("Agricultures")
                        .HasForeignKey("ParcelleRef")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Agriculteur");

                    b.Navigation("Parcelle");
                });

            modelBuilder.Entity("AM.ApplicationCore.Domain.Parcelle", b =>
                {
                    b.HasOne("AM.ApplicationCore.Domain.Cooperative", "CooperativeFK")
                        .WithMany("Parcelles")
                        .HasForeignKey("CooperativeRef")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("CooperativeFK");
                });

            modelBuilder.Entity("AM.ApplicationCore.Domain.Agriculteur", b =>
                {
                    b.Navigation("Agricultures");
                });

            modelBuilder.Entity("AM.ApplicationCore.Domain.Cooperative", b =>
                {
                    b.Navigation("Parcelles");
                });

            modelBuilder.Entity("AM.ApplicationCore.Domain.Parcelle", b =>
                {
                    b.Navigation("Agricultures");
                });
#pragma warning restore 612, 618
        }
    }
}
